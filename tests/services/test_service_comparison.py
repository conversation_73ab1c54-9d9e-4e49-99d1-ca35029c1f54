"""
数据采集服务与数据处理服务实现方式比较测试
对比两个服务在任务调度、错误处理、监控等方面的差异
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

# 数据采集服务相关导入
from src.services.data_collection_service.engines.task_scheduler import TaskScheduler
from src.services.data_collection_service.engines.crawler_engine import CrawlerEngine
from src.services.data_collection_service.task_manager import DataCollectionTaskManager
from src.services.data_collection_service.models import DataSource, CrawlTask

# 数据处理服务相关导入
from src.services.data_processing_service.processing_scheduler import DataProcessingScheduler
from src.services.data_processing_service.task_manager import DataProcessingTaskManager
from src.services.data_processing_service.engine import DataProcessingEngine
from src.services.data_processing_service.performance_monitor import PerformanceMonitor
from src.services.data_processing_service.models import DataProcessingStatus


class TestServiceComparison:
    """服务实现方式比较测试类"""

    def setup_method(self):
        """测试前的设置"""
        self.mock_db = Mock()

    @pytest.mark.asyncio
    async def test_task_scheduling_mechanisms(self):
        """测试任务调度机制的差异"""
        
        # === 数据采集服务：APScheduler + 异步队列 ===
        collection_scheduler = TaskScheduler()
        
        # 模拟数据源
        mock_data_source = Mock()
        mock_data_source.id = 1
        mock_data_source.name = "测试数据源"
        mock_data_source.crawl_mode = "interval"
        mock_data_source.crawl_interval = 3600
        mock_data_source.status = "active"
        
        # 测试APScheduler调度
        with patch.object(collection_scheduler, '_get_data_source', return_value=mock_data_source):
            with patch.object(collection_scheduler, 'scheduler') as mock_scheduler:
                success = await collection_scheduler.add_data_source_schedule(mock_data_source)
                
                # 验证APScheduler被调用
                assert success is True
                mock_scheduler.add_job.assert_called_once()
                
                # 验证调度参数
                call_args = mock_scheduler.add_job.call_args
                assert call_args[1]['func'] == collection_scheduler._execute_scheduled_crawl
                assert call_args[1]['id'] == f"source_{mock_data_source.id}"
        
        # === 数据处理服务：Celery分布式任务队列 ===
        processing_task_manager = DataProcessingTaskManager()
        
        # 测试Celery任务提交
        with patch('src.services.data_processing_service.tasks.submit_processing_task') as mock_submit:
            mock_submit.return_value = "task_123"
            
            task_id = await processing_task_manager.submit_single_record_task(
                record_id=1, priority=5
            )
            
            # 验证Celery任务被提交
            assert task_id == "task_123"
            mock_submit.assert_called_once_with(1, 5)
        
        # === 比较结论 ===
        print("\n=== 任务调度机制比较 ===")
        print("数据采集服务：使用APScheduler + 异步队列，适合定时和事件驱动的爬虫任务")
        print("数据处理服务：使用Celery分布式任务队列，适合CPU密集型的数据处理任务")

    @pytest.mark.asyncio
    async def test_error_handling_strategies(self):
        """测试错误处理策略的差异"""
        
        # === 数据采集服务：基于健康评分的错误处理 ===
        mock_data_source = Mock()
        mock_data_source.id = 1
        mock_data_source.name = "测试数据源"
        mock_data_source.consecutive_error_count = 0
        mock_data_source.max_consecutive_errors = 3
        mock_data_source.health_score = 100.0
        mock_data_source.status = "active"
        
        # 模拟连续错误
        for i in range(4):  # 超过最大错误次数
            mock_data_source.consecutive_error_count += 1
            if mock_data_source.consecutive_error_count >= mock_data_source.max_consecutive_errors:
                mock_data_source.status = "disabled"
                mock_data_source.health_score = 0.0
        
        # 验证数据源被自动禁用
        assert mock_data_source.status == "disabled"
        assert mock_data_source.health_score == 0.0
        
        # === 数据处理服务：基于Celery的重试机制 ===
        processing_engine = DataProcessingEngine()
        
        # 模拟重试逻辑
        with patch.object(processing_engine, '_get_failed_records') as mock_get_failed:
            mock_failed_record = Mock()
            mock_failed_record.id = 1
            mock_failed_record.retry_count = 1
            mock_get_failed.return_value = [mock_failed_record]
            
            with patch.object(processing_engine, 'process_single_record') as mock_process:
                mock_process.return_value = {"success": True}
                
                # 执行重试
                stats = await processing_engine.retry_failed_records(max_retries=3)
                
                # 验证重试统计
                assert "retried" in stats
                assert "success" in stats
                assert "failed" in stats
        
        # === 比较结论 ===
        print("\n=== 错误处理策略比较 ===")
        print("数据采集服务：基于健康评分，自动禁用有问题的数据源，防止资源浪费")
        print("数据处理服务：基于Celery重试机制，支持指数退避和最大重试次数限制")

    @pytest.mark.asyncio
    async def test_monitoring_approaches(self):
        """测试监控方式的差异"""
        
        # === 数据采集服务：基于任务状态的监控 ===
        crawler_engine = CrawlerEngine()
        
        # 模拟引擎状态
        crawler_engine.is_running = True
        crawler_engine.start_time = datetime.now(timezone.utc)
        crawler_engine.running_tasks = {"task_1": Mock(), "task_2": Mock()}
        crawler_engine.completed_tasks = {"task_3": Mock()}
        
        # 获取引擎状态
        status = await crawler_engine.get_status()
        
        # 验证状态信息
        assert status["is_running"] is True
        assert status["running_tasks_count"] == 2
        assert status["completed_tasks_count"] == 1
        assert "uptime_seconds" in status
        
        # === 数据处理服务：基于性能指标的监控 ===
        performance_monitor = PerformanceMonitor()
        
        # 模拟性能监控
        with patch('psutil.cpu_percent', return_value=75.0):
            with patch('psutil.virtual_memory') as mock_memory:
                mock_memory.return_value.percent = 60.0
                mock_memory.return_value.available = 4 * 1024 * 1024 * 1024  # 4GB
                
                with patch('psutil.disk_usage') as mock_disk:
                    mock_disk.return_value.percent = 45.0
                    mock_disk.return_value.free = 100 * 1024 * 1024 * 1024  # 100GB
                    
                    # 启动监控
                    performance_monitor.start_monitoring(interval=1.0)
                    
                    # 等待一个监控周期
                    await asyncio.sleep(1.5)
                    
                    # 停止监控
                    performance_monitor.stop_monitoring()
                    
                    # 验证系统指标被收集
                    assert len(performance_monitor.system_metrics) > 0
                    
                    latest_metric = performance_monitor.system_metrics[-1]
                    assert latest_metric['cpu_percent'] == 75.0
                    assert latest_metric['memory_percent'] == 60.0
        
        # === 比较结论 ===
        print("\n=== 监控方式比较 ===")
        print("数据采集服务：关注任务执行状态、数据源健康度、爬取成功率")
        print("数据处理服务：关注系统资源使用率、处理性能指标、错误统计")

    @pytest.mark.asyncio
    async def test_task_execution_models(self):
        """测试任务执行模式的差异"""
        
        # === 数据采集服务：异步协程模式 ===
        mock_crawler = AsyncMock()
        mock_crawler.crawl.return_value = Mock(
            items_collected=10,
            items_failed=1,
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc),
            errors=[],
            metadata={}
        )
        
        # 模拟异步爬虫执行
        async def simulate_async_crawling():
            """模拟异步爬虫执行"""
            tasks = []
            for i in range(3):  # 并发执行3个爬虫任务
                task = asyncio.create_task(mock_crawler.crawl())
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            return results
        
        # 执行异步任务
        results = await simulate_async_crawling()
        assert len(results) == 3
        
        # === 数据处理服务：同步任务模式（在Celery worker中） ===
        def simulate_sync_processing():
            """模拟同步处理任务"""
            # 这里模拟Celery worker中的同步执行
            processing_results = []
            
            for i in range(3):
                # 模拟同步处理逻辑
                result = {
                    "record_id": i + 1,
                    "success": True,
                    "processing_time": 2.5,
                    "stage": "completed"
                }
                processing_results.append(result)
            
            return processing_results
        
        # 执行同步任务
        sync_results = simulate_sync_processing()
        assert len(sync_results) == 3
        
        # === 比较结论 ===
        print("\n=== 任务执行模式比较 ===")
        print("数据采集服务：异步协程模式，高并发I/O操作，适合网络爬虫")
        print("数据处理服务：同步任务模式，CPU密集型操作，通过Celery实现分布式处理")

    @pytest.mark.asyncio
    async def test_configuration_management(self):
        """测试配置管理方式的差异"""
        
        # === 数据采集服务：基于数据源的动态配置 ===
        mock_data_source = Mock()
        mock_data_source.id = 1
        mock_data_source.name = "测试数据源"
        mock_data_source.collection_method = "api_json"
        mock_data_source.crawl_interval = 3600
        mock_data_source.max_concurrent_tasks = 2
        mock_data_source.request_config = {
            "timeout": 30,
            "headers": {"User-Agent": "FinSight-Bot"},
            "retry_count": 3
        }
        
        # 验证动态配置
        assert mock_data_source.request_config["timeout"] == 30
        assert mock_data_source.max_concurrent_tasks == 2
        
        # === 数据处理服务：基于管道的配置管理 ===
        from src.services.data_processing_service.pipeline_configs import PipelineConfigManager
        
        config_manager = PipelineConfigManager()
        
        # 获取快讯处理管道配置
        flash_news_config = config_manager.get_config("flash_news_pipeline")
        
        # 验证管道配置结构
        assert "data_extraction_config" in flash_news_config
        assert "data_cleaning_config" in flash_news_config
        assert "data_transformation_config" in flash_news_config
        assert "data_validation_config" in flash_news_config
        assert "data_enrichment_config" in flash_news_config
        
        # 验证具体配置项
        extraction_config = flash_news_config["data_extraction_config"]
        assert "title_selectors" in extraction_config
        assert "content_selectors" in extraction_config
        
        # === 比较结论 ===
        print("\n=== 配置管理方式比较 ===")
        print("数据采集服务：基于数据源的动态配置，支持运行时修改爬虫参数")
        print("数据处理服务：基于管道的静态配置，预定义处理流程和规则")

    def test_architecture_summary(self):
        """架构总结对比"""
        
        print("\n" + "="*60)
        print("服务架构对比总结")
        print("="*60)
        
        comparison_table = [
            ["对比维度", "数据采集服务", "数据处理服务"],
            ["-"*20, "-"*30, "-"*30],
            ["任务调度", "APScheduler + 异步队列", "Celery分布式任务队列"],
            ["执行模式", "异步协程 (asyncio)", "同步任务 (Celery worker)"],
            ["错误处理", "健康评分 + 自动禁用", "重试机制 + 指数退避"],
            ["监控方式", "任务状态 + 数据源健康度", "系统资源 + 性能指标"],
            ["配置管理", "数据源动态配置", "管道静态配置"],
            ["适用场景", "I/O密集型网络爬虫", "CPU密集型数据处理"],
            ["扩展性", "单机高并发", "分布式水平扩展"],
            ["容错性", "数据源级别容错", "任务级别重试"],
        ]
        
        for row in comparison_table:
            print(f"{row[0]:<20} | {row[1]:<30} | {row[2]:<30}")
        
        print("\n" + "="*60)
        print("结论：两个服务采用了不同的架构模式，各自适应其业务特点")
        print("="*60)
