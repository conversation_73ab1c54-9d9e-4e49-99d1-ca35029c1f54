# FinSight Backend 运行指南

## 环境准备

### 1. 虚拟环境设置

首先确保您已经创建并激活了虚拟环境：

```bash
# 创建虚拟环境（如果还没有）
python -m venv venv/finsight

# 激活虚拟环境（必须在运行前执行）
source venv/finsight/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境变量配置

项目支持多种环境配置，通过不同的`.env`文件管理：

#### 开发环境（Development）
```bash
# 复制示例配置文件
cp env_example.txt .env.develop

# 编辑开发环境配置
vim .env.develop
```

#### 测试环境（Testing）
```bash
# 创建测试环境配置
cp env_example.txt .env.test

# 编辑测试环境配置
vim .env.test
```

#### 生产环境（Production）
```bash
# 创建生产环境配置
cp env_example.txt .env.production

# 编辑生产环境配置
vim .env.production
```

## 启动方式

### 1. 开发环境启动

#### 方式一：使用启动脚本（推荐）
```bash
# 启动开发环境
./start_dev.sh
```

#### 方式二：使用启动脚本 + 测试
```bash
# 启动开发环境并运行测试
./start_dev_with_tests.sh
```

#### 方式三：手动启动
```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=development

# 启动应用
python -m src.main
# 启动任务
celery -A src.core.celery_app worker --loglevel=info --queues=data_processing,celery --concurrency=2
```

### 2. 测试环境启动

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=testing

# 启动应用
python -m src.main
```

### 3. 生产环境启动

```bash
# 激活虚拟环境
source venv/finsight/bin/activate

# 设置环境变量
export ENVIRONMENT=production

# 启动应用
python -m src.main
```

### 4. 使用Uvicorn直接启动

```bash
# 开发环境（带热重载）
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

# 生产环境
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 5. 使用Docker启动

```bash
# 构建镜像
docker build -t finsight-backend .

# 运行容器
docker run -p 8000:8000 finsight-backend

# 或使用docker-compose
docker-compose up -d
```

## 环境变量说明

### 基础配置
- `ENVIRONMENT`: 环境类型（development/testing/production）
- `DEBUG`: 调试模式（true/false）
- `HOST`: 服务监听地址（默认：0.0.0.0）
- `PORT`: 服务端口（默认：8000）

### 数据库配置
- `DATABASE_URL`: 数据库连接URL
- `DATABASE_ENGINE`: 数据库引擎类型

### 安全配置
- `SECRET_KEY`: JWT密钥
- `ACCESS_TOKEN_EXPIRE_MINUTES`: 访问令牌过期时间

### 短信服务配置
- `SMS_PROVIDER`: 短信服务提供商
- `SMS_API_KEY`: 短信服务API密钥
- `SMS_SECRET_KEY`: 短信服务密钥

### CORS配置
- `CORS_ORIGINS`: 允许的跨域源

## 验证启动状态

### 1. 健康检查
访问：http://localhost:8000/health

期望响应：
```json
{
    "status": "ok",
    "app_name": "FinSight Backend",
    "version": "1.0.0",
    "environment": "development"
}
```

### 2. API文档
访问：http://localhost:8000/docs

### 3. 演示测试
```bash
python demo_test.py
```

## 常见问题排查

### 1. 虚拟环境问题
```bash
# 检查虚拟环境是否激活
which python

# 重新激活虚拟环境
source venv/finsight/bin/activate
```

### 2. 依赖问题
```bash
# 重新安装依赖
pip install -r requirements.txt

# 检查依赖版本
pip list
```

### 3. 端口占用
```bash
# 检查端口占用
lsof -i :8000

# 杀死占用进程
kill -9 <PID>
```

### 4. 数据库连接问题
```bash
# 检查数据库配置
cat .env.develop | grep DATABASE

# 测试数据库连接
python -c "from src.core.database import engine; print(engine.url)"
```

### 5. 权限问题
```bash
# 给启动脚本执行权限
chmod +x start_dev.sh
chmod +x start_dev_with_tests.sh
```

## 日志和调试

### 1. 开启调试模式
在`.env`文件中设置：
```
DEBUG=true
LOG_LEVEL=debug
```

### 2. 查看日志
应用日志会输出到控制台，可以通过以下方式查看：
```bash
# 启动应用并保存日志
python -m src.main 2>&1 | tee app.log
```

### 3. 调试工具
```bash
# 使用调试工具
python debug_api.py
```

## 性能优化

### 1. 生产环境优化
```bash
# 使用多进程
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4

# 使用gunicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 2. 数据库连接池
在配置文件中优化数据库连接设置：
```
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
``` 