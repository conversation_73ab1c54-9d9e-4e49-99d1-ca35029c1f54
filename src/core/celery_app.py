"""
Celery应用配置
用于异步任务处理
"""

import os
from celery import Celery
from .config import settings

# 创建Celery应用实例
celery_app = Celery(
    "finsight_backend",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        'src.services.data_processing_service.tasks',
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    
    # 任务路由
    task_routes={
        'src.services.data_processing_service.tasks.*': {'queue': 'data_processing'},
    },
    
    # 任务结果过期时间
    result_expires=3600,
    
    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 任务限制
    task_time_limit=30 * 60,  # 30分钟
    task_soft_time_limit=25 * 60,  # 25分钟
    
    # 任务压缩
    task_compression='gzip',
    result_compression='gzip',
    
    # 监控
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # 任务优先级
    task_inherit_parent_priority=True,
    task_default_priority=5,
    worker_disable_rate_limits=False,
    
    # 批量任务配置
    task_always_eager=False,  # 在测试环境中可以设置为True
    
    # 任务重试策略
    task_retry_jitter=True,
    task_retry_jitter_max=60,
    
    # Beat调度器配置（如果需要定时任务）
    beat_schedule={
        'process-pending-data': {
            'task': 'src.services.data_processing_service.tasks.process_pending_data_batch',
            'schedule': 60.0,  # 每60秒执行一次
            'options': {'queue': 'data_processing'}
        },
        'cleanup-old-tasks': {
            'task': 'src.services.data_processing_service.tasks.cleanup_old_processing_status',
            'schedule': 3600.0,  # 每小时执行一次
            'options': {'queue': 'data_processing'}
        },
    },
)

# 如果是测试环境，使用同步执行
if os.getenv('ENV_FILE') == '.env.test':
    celery_app.conf.task_always_eager = True
    celery_app.conf.task_eager_propagates = True


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed'


# 任务状态常量
class TaskStatus:
    PENDING = 'PENDING'
    STARTED = 'STARTED'
    SUCCESS = 'SUCCESS'
    FAILURE = 'FAILURE'
    RETRY = 'RETRY'
    REVOKED = 'REVOKED'


# 任务优先级常量
class TaskPriority:
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


def get_celery_app():
    """获取Celery应用实例"""
    return celery_app


def create_task_signature(task_name: str, args=None, kwargs=None, **options):
    """
    创建任务签名
    
    Args:
        task_name: 任务名称
        args: 位置参数
        kwargs: 关键字参数
        **options: 任务选项
        
    Returns:
        Signature: 任务签名
    """
    return celery_app.signature(
        task_name,
        args=args or (),
        kwargs=kwargs or {},
        **options
    )


def send_task_async(task_name: str, args=None, kwargs=None, **options):
    """
    异步发送任务
    
    Args:
        task_name: 任务名称
        args: 位置参数
        kwargs: 关键字参数
        **options: 任务选项
        
    Returns:
        AsyncResult: 异步结果
    """
    return celery_app.send_task(
        task_name,
        args=args or (),
        kwargs=kwargs or {},
        **options
    )


def get_task_result(task_id: str):
    """
    获取任务结果
    
    Args:
        task_id: 任务ID
        
    Returns:
        AsyncResult: 异步结果
    """
    from celery.result import AsyncResult
    return AsyncResult(task_id, app=celery_app)


def revoke_task(task_id: str, terminate=False):
    """
    撤销任务
    
    Args:
        task_id: 任务ID
        terminate: 是否终止正在运行的任务
    """
    celery_app.control.revoke(task_id, terminate=terminate)


def get_active_tasks():
    """获取活跃任务列表"""
    inspect = celery_app.control.inspect()
    return inspect.active()


def get_scheduled_tasks():
    """获取计划任务列表"""
    inspect = celery_app.control.inspect()
    return inspect.scheduled()


def get_worker_stats():
    """获取工作节点统计信息"""
    inspect = celery_app.control.inspect()
    return inspect.stats()


def purge_queue(queue_name: str):
    """
    清空队列
    
    Args:
        queue_name: 队列名称
    """
    celery_app.control.purge()


# 任务装饰器工厂
def create_task_decorator(**task_options):
    """
    创建任务装饰器
    
    Args:
        **task_options: 任务选项
        
    Returns:
        Decorator: 任务装饰器
    """
    def decorator(func):
        return celery_app.task(**task_options)(func)
    return decorator


# 常用任务装饰器
data_processing_task = create_task_decorator(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 60},
    queue='data_processing'
)

data_collection_task = create_task_decorator(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 3, 'countdown': 30},
    queue='data_collection'
)

high_priority_task = create_task_decorator(
    bind=True,
    priority=TaskPriority.HIGH,
    autoretry_for=(Exception,),
    retry_kwargs={'max_retries': 5, 'countdown': 30}
)


# 任务监控钩子 (暂时注释掉，避免信号处理器问题)
# def task_success_handler(sender=None, task_id=None, result=None, retval=None, args=None, kwargs=None, **kwds):
#     """任务成功处理器"""
#     print(f"Task {task_id} succeeded with result: {retval}")


# def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, args=None, kwargs=None, **kwds):
#     """任务失败处理器"""
#     print(f"Task {task_id} failed with error: {exception}")


# 注册信号处理器 (暂时注释掉)
# from celery.signals import task_success, task_failure
# task_success.connect(task_success_handler)
# task_failure.connect(task_failure_handler)
