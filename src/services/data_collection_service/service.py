"""
数据采集服务业务逻辑模块
包含数据源、采集任务、事件驱动规则等的管理功能
专注于数据采集功能
"""

import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from ..data_processing_service.models import ProcessingPipeline

from sqlalchemy import Float, and_, case, cast, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from .models import (CrawlTask, DataSource, DataSourceConfig,
                     DataSourceCredential, EventDrivenCrawlRule,
                     RawDataRecord)
from .schemas import (CrawlTaskCreate, CrawlTaskUpdate, DataSourceConfigCreate,
                      DataSourceConfigUpdate, DataSourceCreate,
                      DataSourceCredentialCreate, DataSourceCredentialUpdate,
                      DataSourceUpdate, EventDrivenCrawlRuleCreate, 
                      EventDrivenCrawlRuleUpdate, RawDataRecordCreate, 
                      RawDataRecordUpdate)


class DataSourceService:
    """
    数据源服务类
    提供数据源的增删改查和管理功能
    """

    def __init__(self):
        """初始化数据源服务"""
        self.logger = logging.getLogger(__name__)

    def create_data_source(
        self, db: Session, data_source_data: DataSourceCreate
    ) -> DataSource:
        """
        创建数据源

        Args:
            db: 数据库会话
            data_source_data: 数据源创建数据

        Returns:
            创建的数据源对象

        Raises:
            IntegrityError: 数据源名称已存在
        """
        try:
            # 创建数据源对象
            db_data_source = DataSource(
                name=data_source_data.name,
                collection_method=data_source_data.collection_method.value,
                content_category=data_source_data.content_category.value,
                business_data_type=data_source_data.business_data_type.value,
                base_url=data_source_data.base_url,
                description=data_source_data.description,
                crawl_mode=data_source_data.crawl_mode.value,
                crawl_interval=data_source_data.crawl_interval,
                priority=data_source_data.priority,
                max_concurrent_tasks=data_source_data.max_concurrent_tasks,
                event_driven_config=data_source_data.event_driven_config,
                supports_realtime=data_source_data.supports_realtime,
                use_proxy=data_source_data.use_proxy,
                proxy_pool=data_source_data.proxy_pool,
                request_delay_min=data_source_data.request_delay_min,
                request_delay_max=data_source_data.request_delay_max,
                max_consecutive_errors=data_source_data.max_consecutive_errors,
                created_by=data_source_data.created_by,
                tags=data_source_data.tags or [],
            )

            # 设置下次采集时间（如果是间隔模式）
            if data_source_data.crawl_mode == "interval":
                db_data_source.next_crawl_time = datetime.now(timezone.utc) + timedelta(
                    seconds=data_source_data.crawl_interval
                )

            db.add(db_data_source)
            db.commit()
            db.refresh(db_data_source)

            self.logger.info(f"Created data source: {db_data_source.name}")
            return db_data_source

        except IntegrityError as e:
            db.rollback()
            self.logger.error(
                f"Failed to create data source - name already exists: {data_source_data.name}"
            )
            raise ValueError(
                f"Data source name '{data_source_data.name}' already exists"
            )

    def get_data_source_by_id(
        self, db: Session, data_source_id: int
    ) -> Optional[DataSource]:
        """
        根据ID获取数据源

        Args:
            db: 数据库会话
            data_source_id: 数据源ID

        Returns:
            数据源对象，如果不存在则返回None
        """
        return (
            db.query(DataSource)
            .filter(DataSource.id == data_source_id)
            .first()
        )

    def get_data_source_by_name(self, db: Session, name: str) -> Optional[DataSource]:
        """
        根据名称获取数据源

        Args:
            db: 数据库会话
            name: 数据源名称

        Returns:
            数据源对象，如果不存在则返回None
        """
        return db.query(DataSource).filter(DataSource.name == name).first()

    def get_data_sources(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        collection_method: Optional[str] = None,
        content_category: Optional[str] = None,
    ) -> Tuple[List[DataSource], int]:
        """
        分页获取数据源列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            status: 状态过滤
            collection_method: 采集方式过滤
            content_category: 内容分类过滤

        Returns:
            (数据源列表, 总数)
        """
        query = db.query(DataSource)

        # 添加过滤条件
        if status:
            query = query.filter(DataSource.status == status)
        if collection_method:
            query = query.filter(DataSource.collection_method == collection_method)
        if content_category:
            query = query.filter(DataSource.content_category == content_category)

        # 获取总数
        total = query.count()

        # 分页查询
        data_sources = (
            query.order_by(DataSource.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return data_sources, total

    def update_data_source(
        self, db: Session, data_source_id: int, update_data: DataSourceUpdate
    ) -> Optional[DataSource]:
        """
        更新数据源

        Args:
            db: 数据库会话
            data_source_id: 数据源ID
            update_data: 更新数据

        Returns:
            更新后的数据源对象，如果不存在则返回None
        """
        data_source = self.get_data_source_by_id(db, data_source_id)
        if not data_source:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                # 处理枚举字段
                if hasattr(value, 'value'):
                    value = value.value
                setattr(data_source, field, value)

            # 如果更新了采集间隔或模式，重新计算下次采集时间
            if "crawl_interval" in update_dict or "crawl_mode" in update_dict:
                if data_source.crawl_mode == "interval":
                    data_source.next_crawl_time = datetime.now(timezone.utc) + timedelta(
                        seconds=data_source.crawl_interval
                    )

            db.commit()
            db.refresh(data_source)

            self.logger.info(f"Updated data source: {data_source.name}")
            return data_source

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"Failed to update data source: {e}")
            raise ValueError("Failed to update data source")

    def delete_data_source(self, db: Session, data_source_id: int) -> bool:
        """
        删除数据源

        Args:
            db: 数据库会话
            data_source_id: 数据源ID

        Returns:
            是否删除成功
        """
        data_source = self.get_data_source_by_id(db, data_source_id)
        if not data_source:
                return False

        try:
            # 检查是否有正在运行的任务
            running_tasks = (
                db.query(CrawlTask)
                .filter(
                    CrawlTask.source_id == data_source_id,
                    CrawlTask.status.in_(["pending", "running"]),
                )
                .count()
            )

            if running_tasks > 0:
                raise ValueError(
                    f"Cannot delete data source with {running_tasks} running tasks"
                )

            db.delete(data_source)
            db.commit()

            self.logger.info(f"Deleted data source: {data_source.name}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete data source: {e}")
            raise

    def update_health_status(
        self,
        db: Session,
        data_source_id: int,
        health_score: float,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ) -> Optional[DataSource]:
        """
        更新数据源健康状态

        Args:
            db: 数据库会话
            data_source_id: 数据源ID
            health_score: 健康评分
            is_success: 是否成功
            error_message: 错误信息

        Returns:
            更新后的数据源对象
        """
        data_source = self.get_data_source_by_id(db, data_source_id)
        if not data_source:
                return None

        # 更新健康评分
        data_source.health_score = health_score
        data_source.last_health_check = datetime.now(timezone.utc)

        if is_success:
            data_source.last_success_time = datetime.now(timezone.utc)
            data_source.consecutive_error_count = 0
        else:
            data_source.consecutive_error_count += 1
            data_source.error_count += 1

            # 如果连续错误次数过多，自动禁用数据源
            if (
                data_source.consecutive_error_count
                >= data_source.max_consecutive_errors
            ):
                data_source.status = "disabled"
                self.logger.warning(
                    f"Data source {data_source.name} disabled due to too many errors"
                )

            db.commit()
        db.refresh(data_source)

        return data_source

    def get_data_sources_for_crawl(self, db: Session) -> List[DataSource]:
        """
        获取需要采集的数据源列表

        Args:
            db: 数据库会话

        Returns:
            需要采集的数据源列表
        """
        now = datetime.now(timezone.utc)

        return (
            db.query(DataSource)
            .filter(
                    DataSource.status == "active",
                    DataSource.crawl_mode.in_(["interval", "hybrid"]),
                DataSource.next_crawl_time <= now,
                )
            .order_by(DataSource.priority.desc())
            .all()
        )

    def get_data_source_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取数据源统计信息

        Args:
            db: 数据库会话

        Returns:
            统计信息字典
        """
        total_count = db.query(DataSource).count()
        active_count = db.query(DataSource).filter(DataSource.status == "active").count()
        
        # 计算健康数据源数量（健康评分 > 0.8）
        healthy_count = (
            db.query(DataSource)
            .filter(DataSource.health_score > 0.8)
            .count()
        )

        # 计算平均健康评分
        avg_health_score_result = db.query(func.avg(DataSource.health_score)).scalar()
        avg_health_score = float(avg_health_score_result) if avg_health_score_result else 0.0

        # 计算平均成功率
        avg_success_rate = 0.0
        if total_count > 0:
            success_rate_sum = (
            db.query(
                    func.sum(
                    case(
                            (DataSource.total_crawled_count > 0,
                             cast(DataSource.total_success_count, Float) / DataSource.total_crawled_count),
                            else_=0
                    )
                )
            ).scalar()
        )
            if success_rate_sum:
                avg_success_rate = float(success_rate_sum) / total_count

        return {
            "total_count": total_count,
            "active_count": active_count,
            "healthy_count": healthy_count,
            "avg_health_score": round(avg_health_score, 2),
            "avg_success_rate": round(avg_success_rate, 2),
        }


class EventDrivenCrawlRuleService:
    """
    事件驱动采集规则服务类
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_rule(
        self, db: Session, rule_data: EventDrivenCrawlRuleCreate
    ) -> EventDrivenCrawlRule:
        """
        创建事件驱动采集规则

        Args:
            db: 数据库会话
            rule_data: 规则创建数据

        Returns:
            创建的规则对象
        """
        try:
            # 检查数据源是否存在
            data_source = (
                db.query(DataSource)
                .filter(DataSource.id == rule_data.source_id)
                .first()
            )
            if not data_source:
                raise ValueError(f"Data source {rule_data.source_id} not found")

            # 创建规则对象
            db_rule = EventDrivenCrawlRule(
                source_id=rule_data.source_id,
                rule_name=rule_data.rule_name,
                trigger_type=rule_data.trigger_type.value,
                trigger_config=rule_data.trigger_config,
                advance_minutes=rule_data.advance_minutes,
                delay_minutes=rule_data.delay_minutes,
                repeat_interval_minutes=rule_data.repeat_interval_minutes,
                max_repeat_count=rule_data.max_repeat_count,
                custom_task_config=rule_data.custom_task_config,
                priority_boost=rule_data.priority_boost,
                is_active=rule_data.is_active,
            )

            db.add(db_rule)
            db.commit()
            db.refresh(db_rule)

            self.logger.info(
                f"Created event driven crawl rule: {db_rule.rule_name} for source {data_source.name}"
            )
            return db_rule

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"Failed to create rule - constraint violation: {e}")
            raise ValueError(
                f"Rule name '{rule_data.rule_name}' already exists for this data source"
            )

    def get_rule_by_id(
        self, db: Session, rule_id: int
    ) -> Optional[EventDrivenCrawlRule]:
        """
        根据ID获取规则

        Args:
            db: 数据库会话
            rule_id: 规则ID

        Returns:
            规则对象，如果不存在则返回None
        """
        return (
            db.query(EventDrivenCrawlRule)
            .filter(EventDrivenCrawlRule.id == rule_id)
            .first()
        )

    def get_rules_by_source(
        self, db: Session, source_id: int, is_active: Optional[bool] = None
    ) -> List[EventDrivenCrawlRule]:
        """
        根据数据源获取规则列表

        Args:
            db: 数据库会话
            source_id: 数据源ID
            is_active: 是否启用过滤

        Returns:
            规则列表
        """
        query = db.query(EventDrivenCrawlRule).filter(
            EventDrivenCrawlRule.source_id == source_id
        )

        if is_active is not None:
            query = query.filter(EventDrivenCrawlRule.is_active == is_active)

        return query.order_by(EventDrivenCrawlRule.created_at.desc()).all()

    def get_rules(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        source_id: Optional[int] = None,
        trigger_type: Optional[str] = None,
        is_active: Optional[bool] = None,
    ) -> Tuple[List[EventDrivenCrawlRule], int]:
        """
        分页获取规则列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            source_id: 数据源ID过滤
            trigger_type: 触发类型过滤
            is_active: 是否启用过滤

        Returns:
            (规则列表, 总数)
        """
        query = db.query(EventDrivenCrawlRule)

        # 添加过滤条件
        if source_id:
            query = query.filter(EventDrivenCrawlRule.source_id == source_id)
        if trigger_type:
            query = query.filter(EventDrivenCrawlRule.trigger_type == trigger_type)
        if is_active is not None:
            query = query.filter(EventDrivenCrawlRule.is_active == is_active)

        # 获取总数
        total = query.count()

        # 分页查询
        rules = (
            query.order_by(EventDrivenCrawlRule.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return rules, total

    def update_rule(
        self, db: Session, rule_id: int, update_data: EventDrivenCrawlRuleUpdate
    ) -> Optional[EventDrivenCrawlRule]:
        """
        更新规则

        Args:
            db: 数据库会话
            rule_id: 规则ID
            update_data: 更新数据

        Returns:
            更新后的规则对象，如果不存在则返回None
        """
        rule = self.get_rule_by_id(db, rule_id)
        if not rule:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                # 处理枚举字段
                if hasattr(value, 'value'):
                    value = value.value
                setattr(rule, field, value)

            db.commit()
            db.refresh(rule)

            self.logger.info(f"Updated rule: {rule.rule_name}")
            return rule

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"Failed to update rule: {e}")
            raise ValueError("Failed to update rule")

    def delete_rule(self, db: Session, rule_id: int) -> bool:
        """
        删除规则

        Args:
            db: 数据库会话
            rule_id: 规则ID

        Returns:
            是否删除成功
        """
        rule = self.get_rule_by_id(db, rule_id)
        if not rule:
                return False

        try:
            db.delete(rule)
            db.commit()

            self.logger.info(f"Deleted rule: {rule.rule_name}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete rule: {e}")
            raise

    def trigger_rule(self, db: Session, rule_id: int, event_id: int) -> bool:
        """
        触发规则执行

        Args:
            db: 数据库会话
            rule_id: 规则ID
            event_id: 事件ID

        Returns:
            是否触发成功
        """
        rule = self.get_rule_by_id(db, rule_id)
        if not rule or not rule.is_active:
                return False

        try:
            # 更新触发统计
            rule.trigger_count += 1
            rule.last_triggered_at = datetime.now(timezone.utc)

            # 这里可以添加创建采集任务的逻辑
            # 暂时只更新统计信息

            db.commit()

            self.logger.info(f"Triggered rule: {rule.rule_name} for event {event_id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to trigger rule: {e}")
            return False


class CrawlTaskService:
    """
    采集任务服务类
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_task(self, db: Session, task_data: CrawlTaskCreate) -> CrawlTask:
        """
        创建采集任务

        Args:
            db: 数据库会话
            task_data: 任务创建数据

        Returns:
            创建的任务对象
        """
        try:
            # 检查数据源是否存在
            data_source = (
                db.query(DataSource)
                .filter(DataSource.id == task_data.source_id)
                .first()
            )
            if not data_source:
                raise ValueError(f"Data source {task_data.source_id} not found")

            # 创建任务对象
            db_task = CrawlTask(
                source_id=task_data.source_id,
                task_type=task_data.task_type,
                trigger_type=task_data.trigger_type.value,
                related_event_id=task_data.related_event_id,
                trigger_rule_id=task_data.trigger_rule_id,
                target_url=task_data.target_url,
                task_config=task_data.task_config,
                scheduled_time=task_data.scheduled_time or datetime.now(timezone.utc),
                max_retry_count=task_data.max_retry_count,
            )

            db.add(db_task)
            db.commit()
            db.refresh(db_task)

            self.logger.info(
                f"Created crawl task: {db_task.id} for source {data_source.name}"
            )
            return db_task

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to create task: {e}")
            raise

    def get_task_by_id(self, db: Session, task_id: int) -> Optional[CrawlTask]:
        """
        根据ID获取任务

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            任务对象，如果不存在则返回None
        """
        return db.query(CrawlTask).filter(CrawlTask.id == task_id).first()

    def get_tasks(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        source_id: Optional[int] = None,
        status: Optional[str] = None,
        task_type: Optional[str] = None,
        trigger_type: Optional[str] = None,
    ) -> Tuple[List[CrawlTask], int]:
        """
        分页获取任务列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            source_id: 数据源ID过滤
            status: 状态过滤
            task_type: 任务类型过滤
            trigger_type: 触发类型过滤

        Returns:
            (任务列表, 总数)
        """
        query = db.query(CrawlTask)

        # 添加过滤条件
        if source_id:
            query = query.filter(CrawlTask.source_id == source_id)
        if status:
            query = query.filter(CrawlTask.status == status)
        if task_type:
            query = query.filter(CrawlTask.task_type == task_type)
        if trigger_type:
            query = query.filter(CrawlTask.trigger_type == trigger_type)

        # 获取总数
        total = query.count()

        # 分页查询
        tasks = (
            query.order_by(CrawlTask.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return tasks, total

    def update_task(
        self, db: Session, task_id: int, update_data: CrawlTaskUpdate
    ) -> Optional[CrawlTask]:
        """
        更新任务

        Args:
            db: 数据库会话
            task_id: 任务ID
            update_data: 更新数据

        Returns:
            更新后的任务对象，如果不存在则返回None
        """
        task = self.get_task_by_id(db, task_id)
        if not task:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                # 处理枚举字段
                if hasattr(value, 'value'):
                    value = value.value
                setattr(task, field, value)

            db.commit()
            db.refresh(task)

            self.logger.info(f"Updated task: {task.id}")
            return task

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to update task: {e}")
            raise

    def delete_task(self, db: Session, task_id: int) -> bool:
        """
        删除任务

        Args:
            db: 数据库会话
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        task = self.get_task_by_id(db, task_id)
        if not task:
                return False

        try:
            # 只能删除未开始或已完成的任务
            if task.status in ["running"]:
                raise ValueError("Cannot delete running task")

            db.delete(task)
            db.commit()

            self.logger.info(f"Deleted task: {task.id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete task: {e}")
            raise

    def start_task(
        self, db: Session, task_id: int, worker_id: str, execution_node: str
    ) -> Optional[CrawlTask]:
        """
        开始执行任务

        Args:
            db: 数据库会话
            task_id: 任务ID
            worker_id: 工作线程ID
            execution_node: 执行节点

        Returns:
            更新后的任务对象
        """
        task = self.get_task_by_id(db, task_id)
        if not task or task.status != "pending":
                return None

        try:
            task.status = "running"
            task.started_time = datetime.now(timezone.utc)
            task.worker_id = worker_id
            task.execution_node = execution_node

            db.commit()
            db.refresh(task)

            self.logger.info(f"Started task: {task.id} on worker {worker_id}")
            return task

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to start task: {e}")
            raise

    def complete_task(
        self,
        db: Session,
        task_id: int,
        success: bool,
        items_found: int = 0,
        items_processed: int = 0,
        items_success: int = 0,
        items_failed: int = 0,
        error_message: Optional[str] = None,
        duration_seconds: Optional[int] = None,
        memory_usage_mb: Optional[int] = None,
        network_requests: int = 0,
    ) -> Optional[CrawlTask]:
        """
        完成任务

        Args:
            db: 数据库会话
            task_id: 任务ID
            success: 是否成功
            items_found: 发现项目数
            items_processed: 处理项目数
            items_success: 成功项目数
            items_failed: 失败项目数
            error_message: 错误信息
            duration_seconds: 执行耗时
            memory_usage_mb: 内存使用量
            network_requests: 网络请求数

        Returns:
            更新后的任务对象
        """
        task = self.get_task_by_id(db, task_id)
        if not task or task.status != "running":
                return None

        try:
            task.status = "completed" if success else "failed"
            task.completed_time = datetime.now(timezone.utc)
            task.items_found = items_found
            task.items_processed = items_processed
            task.items_success = items_success
            task.items_failed = items_failed
            task.error_message = error_message
            task.duration_seconds = duration_seconds
            task.memory_usage_mb = memory_usage_mb
            task.network_requests = network_requests

            # 更新数据源统计
            data_source = task.data_source
            data_source.total_crawled_count += 1
            if success:
                data_source.total_success_count += 1
                data_source.last_crawl_time = task.completed_time
                data_source.last_success_time = task.completed_time

            db.commit()
            db.refresh(task)

            self.logger.info(f"Completed task: {task.id} with status {task.status}")
            return task

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to complete task: {e}")
            raise

    def get_pending_tasks(self, db: Session, limit: int = 100) -> List[CrawlTask]:
        """
        获取待执行的任务列表

        Args:
            db: 数据库会话
            limit: 返回记录数

        Returns:
            待执行任务列表
        """
        now = datetime.now(timezone.utc)

        return (
            db.query(CrawlTask)
            .filter(
                    CrawlTask.status == "pending",
                CrawlTask.scheduled_time <= now,
            )
            .order_by(CrawlTask.scheduled_time.asc())
            .limit(limit)
            .all()
        )

    def get_task_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取任务执行统计

        Args:
            db: 数据库会话

        Returns:
            统计信息字典
        """
        total_tasks = db.query(CrawlTask).count()
        pending_tasks = db.query(CrawlTask).filter(CrawlTask.status == "pending").count()
        running_tasks = db.query(CrawlTask).filter(CrawlTask.status == "running").count()
        completed_tasks = db.query(CrawlTask).filter(CrawlTask.status == "completed").count()
        failed_tasks = db.query(CrawlTask).filter(CrawlTask.status == "failed").count()

        # 计算平均执行时间
        avg_duration_result = (
            db.query(func.avg(CrawlTask.duration_seconds))
            .filter(CrawlTask.duration_seconds.isnot(None))
            .scalar()
        )
        avg_duration_seconds = float(avg_duration_result) if avg_duration_result else None

        # 计算成功率
        success_rate_percentage = (
            (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0.0
        )

        return {
            "total_tasks": total_tasks,
            "pending_tasks": pending_tasks,
            "running_tasks": running_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "avg_duration_seconds": avg_duration_seconds,
            "success_rate_percentage": round(success_rate_percentage, 2),
        }


class DataSourceConfigService:
    """
    数据源配置服务类
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_config(
        self, db: Session, config_data: DataSourceConfigCreate
    ) -> DataSourceConfig:
        """
        创建数据源配置

        Args:
            db: 数据库会话
            config_data: 配置创建数据

        Returns:
            创建的配置对象
        """
        try:
            # 检查数据源是否存在
            data_source = (
                db.query(DataSource)
                .filter(DataSource.id == config_data.source_id)
                .first()
            )
            if not data_source:
                raise ValueError(f"Data source {config_data.source_id} not found")

            # 检查版本是否已存在
            existing_config = (
                db.query(DataSourceConfig)
                .filter(
                        DataSourceConfig.source_id == config_data.source_id,
                        DataSourceConfig.version == config_data.version,
                )
                .first()
            )
            if existing_config:
                raise ValueError(
                    f"Config version {config_data.version} already exists for this data source"
                )

            # 创建配置对象
            db_config = DataSourceConfig(
                source_id=config_data.source_id,
                version=config_data.version,
                selector_config=config_data.selector_config,
                headers_config=config_data.headers_config,
                cookies_config=config_data.cookies_config,
                request_params_config=config_data.request_params_config,
                javascript_config=config_data.javascript_config,
                anti_crawler_config=config_data.anti_crawler_config,
                retry_config=config_data.retry_config,
                proxy_config=config_data.proxy_config,
                is_active=config_data.is_active,
                change_reason=config_data.change_reason,
                changed_by=config_data.changed_by,
            )

            # 如果设置为活跃配置，停用其他配置
            if config_data.is_active:
                db.query(DataSourceConfig).filter(
                    DataSourceConfig.source_id == config_data.source_id,
                    DataSourceConfig.is_active == True,
                ).update({"is_active": False})

                # 更新数据源的当前配置版本
                data_source.current_config_version = config_data.version

            db.add(db_config)
            db.commit()
            db.refresh(db_config)

            self.logger.info(
                f"Created config version {db_config.version} for source {data_source.name}"
            )
            return db_config

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"Failed to create config: {e}")
            raise ValueError("Failed to create config")

    def get_config_by_id(
        self, db: Session, config_id: int
    ) -> Optional[DataSourceConfig]:
        """
        根据ID获取配置

        Args:
            db: 数据库会话
            config_id: 配置ID

        Returns:
            配置对象，如果不存在则返回None
        """
        return (
            db.query(DataSourceConfig)
            .filter(DataSourceConfig.id == config_id)
            .first()
        )

    def get_config_by_source_and_version(
        self, db: Session, source_id: int, version: int
    ) -> Optional[DataSourceConfig]:
        """
        根据数据源和版本获取配置

        Args:
            db: 数据库会话
            source_id: 数据源ID
            version: 版本号

        Returns:
            配置对象，如果不存在则返回None
        """
        return (
            db.query(DataSourceConfig)
            .filter(
                    DataSourceConfig.source_id == source_id,
                    DataSourceConfig.version == version,
            )
            .first()
        )

    def get_active_config_by_source(
        self, db: Session, source_id: int
    ) -> Optional[DataSourceConfig]:
        """
        获取数据源的活跃配置

        Args:
            db: 数据库会话
            source_id: 数据源ID

        Returns:
            活跃配置对象，如果不存在则返回None
        """
        return (
            db.query(DataSourceConfig)
            .filter(
                    DataSourceConfig.source_id == source_id,
                    DataSourceConfig.is_active == True,
            )
            .first()
        )

    def get_configs_by_source(
        self, db: Session, source_id: int, include_inactive: bool = False
    ) -> List[DataSourceConfig]:
        """
        获取数据源的所有配置

        Args:
            db: 数据库会话
            source_id: 数据源ID
            include_inactive: 是否包含非活跃配置

        Returns:
            配置列表
        """
        query = db.query(DataSourceConfig).filter(
            DataSourceConfig.source_id == source_id
        )

        if not include_inactive:
            query = query.filter(DataSourceConfig.is_active == True)

        return query.order_by(DataSourceConfig.version.desc()).all()

    def get_configs(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        source_id: Optional[int] = None,
        is_active: Optional[bool] = None,
        is_validated: Optional[bool] = None,
    ) -> Tuple[List[DataSourceConfig], int]:
        """
        分页获取配置列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            source_id: 数据源ID过滤
            is_active: 是否活跃过滤
            is_validated: 是否已验证过滤

        Returns:
            (配置列表, 总数)
        """
        query = db.query(DataSourceConfig)

        # 添加过滤条件
        if source_id:
            query = query.filter(DataSourceConfig.source_id == source_id)
        if is_active is not None:
            query = query.filter(DataSourceConfig.is_active == is_active)
        if is_validated is not None:
            query = query.filter(DataSourceConfig.is_validated == is_validated)

        # 获取总数
        total = query.count()

        # 分页查询
        configs = (
            query.order_by(DataSourceConfig.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return configs, total

    def update_config(
        self, db: Session, config_id: int, update_data: DataSourceConfigUpdate
    ) -> Optional[DataSourceConfig]:
        """
        更新配置

        Args:
            db: 数据库会话
            config_id: 配置ID
            update_data: 更新数据

        Returns:
            更新后的配置对象，如果不存在则返回None
        """
        config = self.get_config_by_id(db, config_id)
        if not config:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(config, field, value)

            # 如果激活此配置，停用其他配置
            if update_dict.get("is_active"):
                db.query(DataSourceConfig).filter(
                    DataSourceConfig.source_id == config.source_id,
                    DataSourceConfig.id != config.id,
                    DataSourceConfig.is_active == True,
                ).update({"is_active": False})

                # 更新数据源的当前配置版本
                data_source = config.data_source
                data_source.current_config_version = config.version

            db.commit()
            db.refresh(config)

            self.logger.info(f"Updated config: {config.id}")
            return config

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to update config: {e}")
            raise

    def delete_config(self, db: Session, config_id: int) -> bool:
        """
        删除配置

        Args:
            db: 数据库会话
            config_id: 配置ID

        Returns:
            是否删除成功
        """
        config = self.get_config_by_id(db, config_id)
        if not config:
                return False

        try:
            # 不能删除活跃配置
            if config.is_active:
                raise ValueError("Cannot delete active config")

            db.delete(config)
            db.commit()

            self.logger.info(f"Deleted config: {config.id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete config: {e}")
            raise

    def activate_config(
        self, db: Session, config_id: int
    ) -> Optional[DataSourceConfig]:
        """
        激活配置

        Args:
            db: 数据库会话
            config_id: 配置ID

        Returns:
            激活后的配置对象
        """
        config = self.get_config_by_id(db, config_id)
        if not config:
                return None

        try:
            # 停用其他配置
            db.query(DataSourceConfig).filter(
                DataSourceConfig.source_id == config.source_id,
                DataSourceConfig.is_active == True,
            ).update({"is_active": False})

            # 激活当前配置
            config.is_active = True

            # 更新数据源的当前配置版本
            data_source = config.data_source
            data_source.current_config_version = config.version

            db.commit()
            db.refresh(config)

            self.logger.info(f"Activated config: {config.id}")
            return config

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to activate config: {e}")
            raise

    def validate_config(
        self, db: Session, config_id: int, validation_result: Dict[str, Any]
    ) -> Optional[DataSourceConfig]:
        """
        验证配置

        Args:
            db: 数据库会话
            config_id: 配置ID
            validation_result: 验证结果

        Returns:
            验证后的配置对象
        """
        config = self.get_config_by_id(db, config_id)
        if not config:
                return None

        try:
            config.is_validated = validation_result.get("is_valid", False)
            config.validation_result = validation_result

            db.commit()
            db.refresh(config)

            self.logger.info(f"Validated config: {config.id}")
            return config

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to validate config: {e}")
            raise


class DataSourceCredentialService:
    """
    数据源凭证服务类
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_credential(
        self, db: Session, credential_data: DataSourceCredentialCreate
    ) -> DataSourceCredential:
        """
        创建数据源凭证

        Args:
            db: 数据库会话
            credential_data: 凭证创建数据

        Returns:
            创建的凭证对象
        """
        try:
            # 检查数据源是否存在
            data_source = (
                db.query(DataSource)
                .filter(DataSource.id == credential_data.source_id)
                .first()
            )
            if not data_source:
                raise ValueError(f"Data source {credential_data.source_id} not found")

            # 加密凭证数据
            encrypted_data = self._encrypt_credential_data(credential_data.credential_data)

            # 创建凭证对象
            db_credential = DataSourceCredential(
                source_id=credential_data.source_id,
                credential_type=credential_data.credential_type.value,
                encrypted_data=encrypted_data["encrypted_data"],
                encryption_method=encrypted_data["encryption_method"],
                salt=encrypted_data["salt"],
                expires_at=credential_data.expires_at,
            )

            db.add(db_credential)
            db.commit()
            db.refresh(db_credential)

            self.logger.info(
                f"Created credential for source {data_source.name} with type {credential_data.credential_type.value}"
            )
            return db_credential

        except IntegrityError as e:
            db.rollback()
            self.logger.error(f"Failed to create credential: {e}")
            raise ValueError("Credential with this type already exists for this data source")

    def get_credential_by_id(
        self, db: Session, credential_id: int
    ) -> Optional[DataSourceCredential]:
        """
        根据ID获取凭证

        Args:
            db: 数据库会话
            credential_id: 凭证ID

        Returns:
            凭证对象，如果不存在则返回None
        """
        return (
            db.query(DataSourceCredential)
            .filter(DataSourceCredential.id == credential_id)
            .first()
        )

    def get_credentials_by_source(
        self, db: Session, source_id: int, include_inactive: bool = False
    ) -> List[DataSourceCredential]:
        """
        获取数据源的所有凭证

        Args:
            db: 数据库会话
            source_id: 数据源ID
            include_inactive: 是否包含非活跃凭证

        Returns:
            凭证列表
        """
        query = db.query(DataSourceCredential).filter(
            DataSourceCredential.source_id == source_id
        )

        if not include_inactive:
            query = query.filter(DataSourceCredential.is_active == True)

        return query.order_by(DataSourceCredential.created_at.desc()).all()

    def get_active_credential_by_source_and_type(
        self, db: Session, source_id: int, credential_type: str
    ) -> Optional[DataSourceCredential]:
        """
        获取指定类型的活跃凭证

        Args:
            db: 数据库会话
            source_id: 数据源ID
            credential_type: 凭证类型

        Returns:
            凭证对象，如果不存在则返回None
        """
        return (
            db.query(DataSourceCredential)
            .filter(
                    DataSourceCredential.source_id == source_id,
                    DataSourceCredential.credential_type == credential_type,
                    DataSourceCredential.is_active == True,
            )
            .first()
        )

    def get_credentials(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        source_id: Optional[int] = None,
        credential_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        validation_status: Optional[str] = None,
    ) -> Tuple[List[DataSourceCredential], int]:
        """
        分页获取凭证列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            source_id: 数据源ID过滤
            credential_type: 凭证类型过滤
            is_active: 是否活跃过滤
            validation_status: 验证状态过滤

        Returns:
            (凭证列表, 总数)
        """
        query = db.query(DataSourceCredential)

        # 添加过滤条件
        if source_id:
            query = query.filter(DataSourceCredential.source_id == source_id)
        if credential_type:
            query = query.filter(DataSourceCredential.credential_type == credential_type)
        if is_active is not None:
            query = query.filter(DataSourceCredential.is_active == is_active)
        if validation_status:
            query = query.filter(DataSourceCredential.validation_status == validation_status)

        # 获取总数
        total = query.count()

        # 分页查询
        credentials = (
            query.order_by(DataSourceCredential.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return credentials, total

    def update_credential(
        self, db: Session, credential_id: int, update_data: DataSourceCredentialUpdate
    ) -> Optional[DataSourceCredential]:
        """
        更新凭证

        Args:
            db: 数据库会话
            credential_id: 凭证ID
            update_data: 更新数据

        Returns:
            更新后的凭证对象，如果不存在则返回None
        """
        credential = self.get_credential_by_id(db, credential_id)
        if not credential:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)

            # 如果更新凭证数据，需要重新加密
            if "credential_data" in update_dict:
                encrypted_data = self._encrypt_credential_data(update_dict["credential_data"])
                credential.encrypted_data = encrypted_data["encrypted_data"]
                credential.encryption_method = encrypted_data["encryption_method"]
                credential.salt = encrypted_data["salt"]
                del update_dict["credential_data"]

            for field, value in update_dict.items():
                # 处理枚举字段
                if hasattr(value, 'value'):
                    value = value.value
                setattr(credential, field, value)

            db.commit()
            db.refresh(credential)

            self.logger.info(f"Updated credential: {credential.id}")
            return credential

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to update credential: {e}")
            raise

    def delete_credential(self, db: Session, credential_id: int) -> bool:
        """
        删除凭证

        Args:
            db: 数据库会话
            credential_id: 凭证ID

        Returns:
            是否删除成功
        """
        credential = self.get_credential_by_id(db, credential_id)
        if not credential:
                return False

        try:
            db.delete(credential)
            db.commit()

            self.logger.info(f"Deleted credential: {credential.id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete credential: {e}")
            raise

    def validate_credential(
        self, db: Session, credential_id: int, is_valid: bool
    ) -> Optional[DataSourceCredential]:
        """
        验证凭证

        Args:
            db: 数据库会话
            credential_id: 凭证ID
            is_valid: 是否有效

        Returns:
            验证后的凭证对象
        """
        credential = self.get_credential_by_id(db, credential_id)
        if not credential:
                return None

        try:
            credential.validation_status = "valid" if is_valid else "invalid"
            credential.last_validated = datetime.now(timezone.utc)

            db.commit()
            db.refresh(credential)

            self.logger.info(f"Validated credential: {credential.id} with status {credential.validation_status}")
            return credential

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to validate credential: {e}")
            raise

    def get_decrypted_credential_data(
        self, db: Session, credential_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        获取解密后的凭证数据

        Args:
            db: 数据库会话
            credential_id: 凭证ID

        Returns:
            解密后的凭证数据
        """
        credential = self.get_credential_by_id(db, credential_id)
        if not credential:
                return None

        try:
            return self._decrypt_credential_data(
                credential.encrypted_data, credential.encryption_method
            )
        except Exception as e:
            self.logger.error(f"Failed to decrypt credential data: {e}")
            return None

    def _encrypt_credential_data(
        self, credential_data: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        加密凭证数据
        
        注意：这里只是示例实现，实际应该使用真正的加密算法

        Args:
            credential_data: 原始凭证数据

        Returns:
            加密后的数据字典
        """
        import base64
        import secrets

        # 生成盐值
        salt = secrets.token_hex(16)
        
        # 简化的加密实现（实际应该使用AES等加密算法）
        data_str = json.dumps(credential_data)
        encoded_data = base64.b64encode(data_str.encode()).decode()

        return {
            "encrypted_data": encoded_data,
            "encryption_method": "AES-256-GCM",
            "salt": salt,
        }

    def _decrypt_credential_data(
        self, encrypted_data: str, encryption_method: str
    ) -> Dict[str, Any]:
        """
        解密凭证数据
        
        注意：这里只是示例实现，实际应该使用真正的解密算法

        Args:
            encrypted_data: 加密数据
            encryption_method: 加密方法

        Returns:
            解密后的凭证数据
        """
        import base64

        # 简化的解密实现
        decoded_data = base64.b64decode(encrypted_data.encode()).decode()
        return json.loads(decoded_data)


class RawDataRecordService:
    """
    原始数据记录服务类
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_raw_data_record(
        self, db: Session, record_data: RawDataRecordCreate
    ) -> RawDataRecord:
        """
        创建原始数据记录

        Args:
            db: 数据库会话
            record_data: 记录创建数据

        Returns:
            创建的记录对象
        """
        try:
            # 检查数据源是否存在
            data_source = (
                db.query(DataSource)
                .filter(DataSource.id == record_data.source_id)
                .first()
            )
            if not data_source:
                raise ValueError(f"Data source {record_data.source_id} not found")

            # 创建记录对象
            db_record = RawDataRecord(
                task_id=record_data.task_id,
                source_id=record_data.source_id,
                source_url=record_data.source_url,
                canonical_url=record_data.canonical_url,
                url_hash=record_data.url_hash,
                url_domain=record_data.url_domain,
                content_hash=record_data.content_hash,
                content_simhash=record_data.content_simhash,
                content_length=record_data.content_length,
                content_encoding=record_data.content_encoding,
                title=record_data.title,
                author=record_data.author,
                publish_time=record_data.publish_time,
                crawl_time=record_data.crawl_time or datetime.now(timezone.utc),
                mongodb_id=record_data.mongodb_id,
                mongodb_collection=record_data.mongodb_collection,
                content_type=record_data.content_type,
                processing_status=record_data.processing_status,
                processing_priority=record_data.processing_priority,
                quality_score=record_data.quality_score,
                retention_policy=record_data.retention_policy,
                archive_after_days=record_data.archive_after_days,
                delete_after_days=record_data.delete_after_days,
            )

            db.add(db_record)
            db.commit()
            db.refresh(db_record)

            self.logger.info(f"Created raw data record: {db_record.id}")
            return db_record

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to create raw data record: {e}")
            raise

    def get_raw_data_record_by_id(
        self, db: Session, record_id: int
    ) -> Optional[RawDataRecord]:
        """
        根据ID获取原始数据记录

        Args:
            db: 数据库会话
            record_id: 记录ID

        Returns:
            记录对象，如果不存在则返回None
        """
        return db.query(RawDataRecord).filter(RawDataRecord.id == record_id).first()

    def get_raw_data_records(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        source_id: Optional[int] = None,
        task_id: Optional[int] = None,
        processing_status: Optional[str] = None,
        is_archived: Optional[bool] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Tuple[List[RawDataRecord], int]:
        """
        分页获取原始数据记录列表

        Args:
            db: 数据库会话
            skip: 跳过记录数
            limit: 返回记录数
            source_id: 数据源ID过滤
            task_id: 任务ID过滤
            processing_status: 处理状态过滤
            is_archived: 是否归档过滤
            start_date: 开始日期过滤
            end_date: 结束日期过滤

        Returns:
            (记录列表, 总数)
        """
        query = db.query(RawDataRecord)

        # 添加过滤条件
        if source_id:
            query = query.filter(RawDataRecord.source_id == source_id)
        if task_id:
            query = query.filter(RawDataRecord.task_id == task_id)
        if processing_status:
            query = query.filter(RawDataRecord.processing_status == processing_status)
        if is_archived is not None:
            query = query.filter(RawDataRecord.is_archived == is_archived)
        if start_date:
            query = query.filter(RawDataRecord.crawl_time >= start_date)
        if end_date:
            query = query.filter(RawDataRecord.crawl_time <= end_date)

        # 获取总数
        total = query.count()

        # 分页查询
        records = (
            query.order_by(RawDataRecord.crawl_time.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        return records, total

    def update_raw_data_record(
        self, db: Session, record_id: int, update_data: RawDataRecordUpdate
    ) -> Optional[RawDataRecord]:
        """
        更新原始数据记录

        Args:
            db: 数据库会话
            record_id: 记录ID
            update_data: 更新数据

        Returns:
            更新后的记录对象，如果不存在则返回None
        """
        record = self.get_raw_data_record_by_id(db, record_id)
        if not record:
                return None

        try:
            # 更新字段
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(record, field, value)

            db.commit()
            db.refresh(record)

            self.logger.info(f"Updated raw data record: {record.id}")
            return record

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to update raw data record: {e}")
            raise

    def delete_raw_data_record(self, db: Session, record_id: int) -> bool:
        """
        删除原始数据记录

        Args:
            db: 数据库会话
            record_id: 记录ID

        Returns:
            是否删除成功
        """
        record = self.get_raw_data_record_by_id(db, record_id)
        if not record:
                return False

        try:
            db.delete(record)
            db.commit()

            self.logger.info(f"Deleted raw data record: {record.id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to delete raw data record: {e}")
            raise

    def get_raw_data_record_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取原始数据记录统计

        Args:
            db: 数据库会话

        Returns:
            统计信息字典
        """
        total_records = db.query(RawDataRecord).count()
        pending_records = db.query(RawDataRecord).filter(RawDataRecord.processing_status == "pending").count()
        processing_records = db.query(RawDataRecord).filter(RawDataRecord.processing_status == "processing").count()
        processed_records = db.query(RawDataRecord).filter(RawDataRecord.processing_status == "processed").count()
        failed_records = db.query(RawDataRecord).filter(RawDataRecord.processing_status == "failed").count()
        archived_records = db.query(RawDataRecord).filter(RawDataRecord.is_archived == True).count()

        # 计算平均质量评分
        avg_quality_result = (
            db.query(func.avg(RawDataRecord.quality_score))
            .filter(RawDataRecord.quality_score.isnot(None))
            .scalar()
        )
        avg_quality_score = float(avg_quality_result) if avg_quality_result else None

        # 按数据源统计
        source_stats = (
                db.query(
                RawDataRecord.source_id,
                    func.count(RawDataRecord.id).label("count")
                )
            .group_by(RawDataRecord.source_id)
                .limit(10)
                .all()
            )
        return {
            "total_records": total_records,
            "pending_records": pending_records,
            "processing_records": processing_records,
            "processed_records": processed_records,
            "failed_records": failed_records,
            "archived_records": archived_records,
            "avg_quality_score": round(avg_quality_score, 2) if avg_quality_score else None,
            "source_stats": [{"source_id": s[0], "count": s[1]} for s in source_stats],
        }

    def archive_raw_data_record(self, db: Session, record_id: int) -> bool:
        """
        归档原始数据记录

        Args:
            db: 数据库会话
            record_id: 记录ID

        Returns:
            是否归档成功
        """
        record = self.get_raw_data_record_by_id(db, record_id)
        if not record:
                return False

        try:
            record.is_archived = True
            record.archived_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(record)

            self.logger.info(f"Archived raw data record: {record.id}")
            return True

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to archive raw data record: {e}")
            raise

    def batch_update_processing_status(
        self, 
        db: Session, 
        record_ids: List[int], 
        new_status: str
    ) -> int:
        """
        批量更新处理状态

        Args:
            db: 数据库会话
            record_ids: 记录ID列表
            new_status: 新状态

        Returns:
            更新的记录数量
        """
        try:
            updated_count = (
                db.query(RawDataRecord)
                .filter(RawDataRecord.id.in_(record_ids))
                .update(
                    {"processing_status": new_status},
                    synchronize_session=False
                )
            )
            
            db.commit()

            self.logger.info(f"Batch updated {updated_count} records to status {new_status}")
            return updated_count

        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to batch update processing status: {e}")
            raise

    def get_duplicate_records(
        self, db: Session, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取重复记录

        Args:
            db: 数据库会话
            limit: 限制记录数

        Returns:
            重复记录组列表
        """
        # 查找具有相同content_hash的记录
        duplicate_groups = (
            db.query(
                RawDataRecord.content_hash,
                func.count(RawDataRecord.id).label("count")
            )
                .filter(RawDataRecord.content_hash.isnot(None))
                .group_by(RawDataRecord.content_hash)
                .having(func.count(RawDataRecord.id) > 1)
            .order_by(func.count(RawDataRecord.id).desc())
                .limit(limit)
                .all()
            )
            
        result = []
        for content_hash, count in duplicate_groups:
            # 获取该组的记录详情
                records = (
                    db.query(RawDataRecord)
                    .filter(RawDataRecord.content_hash == content_hash)
                .order_by(RawDataRecord.created_at.desc())
                    .all()
                )
                duplicate_info = {
                    "content_hash": content_hash,
                    "count": count,
                    "records": [
                        {
                        "id": r.id,
                        "title": r.title,
                        "source_url": r.source_url,
                        "created_at": r.created_at,
                    }
                    for r in records
                ],
            }
                result.append(duplicate_info)

        return result
    
    def get_task_stats(self, db: Session) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Args:
            db: 数据库会话
            
        Returns:
            Dict: 任务统计信息
        """
        total_tasks = db.query(CrawlTask).count()
        pending_tasks = db.query(CrawlTask).filter(CrawlTask.status == "pending").count()
        running_tasks = db.query(CrawlTask).filter(CrawlTask.status == "running").count()
        queued_tasks = db.query(CrawlTask).filter(CrawlTask.status == "queued").count()
        completed_tasks = db.query(CrawlTask).filter(CrawlTask.status == "completed").count()
        failed_tasks = db.query(CrawlTask).filter(CrawlTask.status == "failed").count()
        cancelled_tasks = db.query(CrawlTask).filter(CrawlTask.status == "cancelled").count()
        
        # 今日任务统计
        today = datetime.now(timezone.utc).date()
        today_tasks = db.query(CrawlTask).filter(
            func.date(CrawlTask.created_at) == today
        ).count()
        
        # 按任务类型统计
        manual_tasks = db.query(CrawlTask).filter(CrawlTask.task_type == "manual").count()
        scheduled_tasks = db.query(CrawlTask).filter(CrawlTask.task_type == "scheduled").count()
        event_driven_tasks = db.query(CrawlTask).filter(CrawlTask.task_type == "event_driven").count()
        
        return {
            "total_tasks": total_tasks,
            "status_breakdown": {
                "pending": pending_tasks,
                "running": running_tasks,
                "queued": queued_tasks,
                "completed": completed_tasks,
                "failed": failed_tasks,
                "cancelled": cancelled_tasks,
            },
            "today_tasks": today_tasks,
            "type_breakdown": {
                "manual": manual_tasks,
                "scheduled": scheduled_tasks,
                "event_driven": event_driven_tasks,
            },
            "active_tasks": pending_tasks + running_tasks + queued_tasks,
        }
    
    def update_task_status(
        self, 
        db: Session, 
        task_id: int, 
        status: str, 
        message: Optional[str] = None
    ) -> bool:
        """
        更新任务状态
        
        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新状态
            message: 状态消息
            
        Returns:
            bool: 是否更新成功
        """
        task = self.get_task_by_id(db, task_id)
        if not task:
            return False
        
        try:
            task.status = status
            if message:
                task.error_message = message
            
            # 更新时间戳
            if status == "running":
                task.started_at = datetime.now(timezone.utc)
            elif status in ["completed", "failed", "cancelled"]:
                task.completed_at = datetime.now(timezone.utc)
            
            db.commit()
            db.refresh(task)
            
            self.logger.info(f"Updated task {task_id} status to {status}")
            return True
            
        except Exception as e:
            db.rollback()
            self.logger.error(f"Failed to update task status: {e}")
            return False
