"""
数据采集服务数据传输对象(DTO)
定义API请求和响应的数据格式
专注于数据采集功能
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, HttpUrl, field_validator


class CollectionMethod(str, Enum):
    """采集方式枚举"""

    API_JSON = "api_json"
    WEB_SCRAPING = "web_scraping"
    API_XML = "api_xml"
    API_RSS = "api_rss"
    WEB_DYNAMIC = "web_dynamic"
    FILE_UPLOAD = "file_upload"


class ContentCategory(str, Enum):
    """内容分类枚举"""

    FINANCIAL_NEWS = "financial_news"
    OFFICIAL_DATA = "official_data"
    RESEARCH_REPORT = "research_report"
    SOCIAL_MEDIA = "social_media"
    REGULATORY_FILING = "regulatory_filing"


class BusinessDataType(str, Enum):
    """业务数据类型枚举"""

    FLASH_NEWS = "flash_news"  # 快讯
    NEWS_ARTICLE = "news_article"  # 新闻文章
    RESEARCH_REPORT = "research_report"  # 研究报告
    ECONOMIC_DATA = "economic_data"  # 经济数据
    COMPANY_ANNOUNCEMENT = "company_announcement"  # 公司公告
    SOCIAL_SENTIMENT = "social_sentiment"  # 社交舆情


class CrawlMode(str, Enum):
    """采集模式枚举"""

    INTERVAL = "interval"
    EVENT_DRIVEN = "event_driven"
    HYBRID = "hybrid"


class DataSourceStatus(str, Enum):
    """数据源状态枚举"""

    ACTIVE = "active"
    INACTIVE = "inactive"
    DISABLED = "disabled"
    MAINTENANCE = "maintenance"


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TriggerType(str, Enum):
    """触发类型枚举"""

    INTERVAL = "interval"
    EVENT = "event"
    MANUAL = "manual"
    FINANCIAL_EVENT = "financial_event"
    TIME_BASED = "time_based"
    EXTERNAL_SIGNAL = "external_signal"


class CredentialType(str, Enum):
    """凭证类型枚举"""

    API_KEY = "api_key"
    USERNAME_PASSWORD = "username_password"
    OAUTH_TOKEN = "oauth_token"
    CERTIFICATE = "certificate"


class ValidationStatus(str, Enum):
    """验证状态枚举"""

    VALID = "valid"
    INVALID = "invalid"
    EXPIRED = "expired"
    UNKNOWN = "unknown"


# === 数据源相关 Schemas ===

class DataSourceCreate(BaseModel):
    """数据源创建请求"""

    name: str = Field(..., min_length=1, max_length=100, description="数据源名称")
    collection_method: CollectionMethod = Field(..., description="数据采集方式")
    content_category: ContentCategory = Field(..., description="内容分类")
    business_data_type: BusinessDataType = Field(
        default=BusinessDataType.NEWS_ARTICLE, description="业务数据类型"
    )
    base_url: Optional[str] = Field(None, max_length=1000, description="基础URL地址")
    description: Optional[str] = Field(None, description="数据源描述")

    # 采集模式配置
    crawl_mode: CrawlMode = Field(default=CrawlMode.INTERVAL, description="采集模式")
    crawl_interval: int = Field(
        default=3600, ge=60, le=86400, description="采集间隔时间(秒)"
    )
    priority: int = Field(default=5, ge=1, le=10, description="采集优先级")
    max_concurrent_tasks: int = Field(
        default=1, ge=1, le=50, description="最大并发任务数"
    )

    # 事件驱动配置
    event_driven_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="事件驱动配置"
    )
    supports_realtime: bool = Field(default=False, description="是否支持实时采集")

    # 反爬虫配置
    use_proxy: bool = Field(default=False, description="是否使用代理")
    proxy_pool: Optional[str] = Field(None, max_length=50, description="代理池标识符")
    request_delay_min: int = Field(
        default=2, ge=0, le=60, description="请求最小延迟时间(秒)"
    )
    request_delay_max: int = Field(
        default=10, ge=0, le=300, description="请求最大延迟时间(秒)"
    )

    # 错误管理
    max_consecutive_errors: int = Field(
        default=10, ge=1, le=100, description="最大连续错误次数"
    )

    # 其他信息
    created_by: Optional[str] = Field(None, max_length=100, description="创建者")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签数组")

    @field_validator("request_delay_max")
    @classmethod
    def validate_delay_range(cls, v, info):
        if info.data.get("request_delay_min") and v < info.data["request_delay_min"]:
            raise ValueError("request_delay_max must be greater than request_delay_min")
        return v


class DataSourceUpdate(BaseModel):
    """数据源更新请求"""

    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="数据源名称"
    )
    collection_method: Optional[CollectionMethod] = Field(
        None, description="数据采集方式"
    )
    content_category: Optional[ContentCategory] = Field(None, description="内容分类")
    business_data_type: Optional[BusinessDataType] = Field(
        None, description="业务数据类型"
    )
    base_url: Optional[str] = Field(None, max_length=1000, description="基础URL地址")
    description: Optional[str] = Field(None, description="数据源描述")

    # 采集模式配置
    crawl_mode: Optional[CrawlMode] = Field(None, description="采集模式")
    crawl_interval: Optional[int] = Field(
        None, ge=60, le=86400, description="采集间隔时间(秒)"
    )
    priority: Optional[int] = Field(None, ge=1, le=10, description="采集优先级")
    max_concurrent_tasks: Optional[int] = Field(
        None, ge=1, le=50, description="最大并发任务数"
    )

    # 事件驱动配置
    event_driven_config: Optional[Dict[str, Any]] = Field(
        None, description="事件驱动配置"
    )
    supports_realtime: Optional[bool] = Field(None, description="是否支持实时采集")

    # 反爬虫配置
    use_proxy: Optional[bool] = Field(None, description="是否使用代理")
    proxy_pool: Optional[str] = Field(None, max_length=50, description="代理池标识符")
    request_delay_min: Optional[int] = Field(
        None, ge=0, le=60, description="请求最小延迟时间(秒)"
    )
    request_delay_max: Optional[int] = Field(
        None, ge=0, le=300, description="请求最大延迟时间(秒)"
    )

    # 状态管理
    status: Optional[DataSourceStatus] = Field(None, description="数据源状态")
    max_consecutive_errors: Optional[int] = Field(
        None, ge=1, le=100, description="最大连续错误次数"
    )

    # 其他信息
    tags: Optional[List[str]] = Field(None, description="标签数组")





class DataSourceResponse(BaseModel):
    """数据源响应"""

    id: int
    name: str
    collection_method: str
    content_category: str
    business_data_type: str = "news_article"
    base_url: Optional[str]
    description: Optional[str]

    # 采集模式配置
    crawl_mode: str = "interval"
    crawl_interval: int = 3600
    priority: int = 5
    max_concurrent_tasks: int = 1

    # 事件驱动配置
    event_driven_config: Optional[Dict[str, Any]] = None
    supports_realtime: Optional[bool] = False

    # 反爬虫配置
    use_proxy: bool = False
    proxy_pool: Optional[str] = None
    request_delay_min: int = 2
    request_delay_max: int = 10

    # 状态管理
    status: str = "active"
    health_score: Optional[Decimal] = Field(default=Decimal("1.00"), description="健康评分")
    last_health_check: Optional[datetime] = None

    # 时间信息
    last_crawl_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    next_crawl_time: Optional[datetime] = None

    # 错误管理
    error_count: Optional[int] = 0
    consecutive_error_count: Optional[int] = 0
    max_consecutive_errors: Optional[int] = 10

    # 统计信息
    total_crawled_count: Optional[int] = 0
    total_success_count: Optional[int] = 0
    avg_response_time_ms: Optional[int] = None

    current_config_version: Optional[int] = 1
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    tags: Optional[List[str]] = Field(default_factory=list, description="标签数组")

    @field_validator("health_score", mode="before")
    @classmethod
    def validate_health_score(cls, v):
        """处理health_score为None的情况"""
        if v is None:
            return Decimal("1.00")
        return v

    @field_validator("tags", mode="before")
    @classmethod
    def validate_tags(cls, v):
        """处理tags为None的情况"""
        if v is None:
            return []
        return v

    class Config:
        from_attributes = True


# === 数据源配置相关 Schemas ===

class DataSourceConfigCreate(BaseModel):
    """数据源配置创建请求"""

    source_id: int = Field(..., description="关联的数据源ID")
    version: int = Field(..., ge=1, description="配置版本号")

    # 数据获取技术配置
    selector_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="选择器配置"
    )
    headers_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="请求头配置"
    )
    cookies_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Cookie配置"
    )
    request_params_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="请求参数配置"
    )

    # 反爬虫和网络请求技术配置
    javascript_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="JavaScript配置"
    )
    anti_crawler_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="反爬虫配置"
    )
    retry_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="重试配置"
    )
    proxy_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="代理配置"
    )

    is_active: bool = Field(default=True, description="是否启用")
    change_reason: Optional[str] = Field(None, description="变更原因")
    changed_by: Optional[str] = Field(None, max_length=100, description="变更人")


class DataSourceConfigUpdate(BaseModel):
    """数据源配置更新请求"""

    # 数据获取技术配置
    selector_config: Optional[Dict[str, Any]] = Field(None, description="选择器配置")
    headers_config: Optional[Dict[str, Any]] = Field(None, description="请求头配置")
    cookies_config: Optional[Dict[str, Any]] = Field(None, description="Cookie配置")
    request_params_config: Optional[Dict[str, Any]] = Field(None, description="请求参数配置")

    # 反爬虫和网络请求技术配置
    javascript_config: Optional[Dict[str, Any]] = Field(
        None, description="JavaScript配置"
    )
    anti_crawler_config: Optional[Dict[str, Any]] = Field(
        None, description="反爬虫配置"
    )
    retry_config: Optional[Dict[str, Any]] = Field(None, description="重试配置")
    proxy_config: Optional[Dict[str, Any]] = Field(None, description="代理配置")

    is_active: Optional[bool] = Field(None, description="是否启用")
    is_validated: Optional[bool] = Field(None, description="是否已验证")
    validation_result: Optional[Dict[str, Any]] = Field(None, description="验证结果")

    change_reason: Optional[str] = Field(None, description="变更原因")
    changed_by: Optional[str] = Field(None, max_length=100, description="变更人")


class DataSourceConfigResponse(BaseModel):
    """数据源配置响应"""

    id: int
    source_id: int
    version: int

    # 数据获取技术配置
    selector_config: Optional[Dict[str, Any]] = {}
    headers_config: Optional[Dict[str, Any]] = {}
    cookies_config: Optional[Dict[str, Any]] = None
    request_params_config: Optional[Dict[str, Any]] = {}

    # 反爬虫和网络请求技术配置
    javascript_config: Optional[Dict[str, Any]] = None
    anti_crawler_config: Optional[Dict[str, Any]] = {}
    retry_config: Optional[Dict[str, Any]] = {}
    proxy_config: Optional[Dict[str, Any]] = {}

    is_active: bool = True
    is_validated: Optional[bool] = False
    validation_result: Optional[Dict[str, Any]] = None

    change_reason: Optional[str] = None
    changed_by: Optional[str] = None
    created_at: Optional[datetime] = None

    @field_validator('selector_config', 'headers_config', 'request_params_config', 'anti_crawler_config', 'retry_config', 'proxy_config', mode='before')
    @classmethod
    def validate_config_dicts(cls, v):
        """确保配置字典字段不为None"""
        return v if v is not None else {}

    class Config:
        from_attributes = True


# === 事件驱动采集规则相关 Schemas ===

class EventDrivenCrawlRuleCreate(BaseModel):
    """事件驱动采集规则创建请求"""

    source_id: int = Field(..., description="关联的数据源ID")
    rule_name: str = Field(..., min_length=1, max_length=100, description="规则名称")

    # 触发条件
    trigger_type: TriggerType = Field(..., description="触发类型")
    trigger_config: Dict[str, Any] = Field(..., description="触发配置")

    # 时间配置
    advance_minutes: int = Field(default=0, ge=0, le=1440, description="提前采集分钟数")
    delay_minutes: int = Field(default=0, ge=0, le=1440, description="延后采集分钟数")
    repeat_interval_minutes: Optional[int] = Field(
        None, ge=1, le=1440, description="重复采集间隔(分钟)"
    )
    max_repeat_count: int = Field(default=1, ge=1, le=100, description="最大重复次数")

    # 采集配置
    custom_task_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="自定义任务配置"
    )
    priority_boost: int = Field(default=0, ge=0, le=5, description="优先级提升")

    is_active: bool = Field(default=True, description="是否启用")


class EventDrivenCrawlRuleUpdate(BaseModel):
    """事件驱动采集规则更新请求"""

    rule_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="规则名称"
    )

    # 触发条件
    trigger_type: Optional[TriggerType] = Field(None, description="触发类型")
    trigger_config: Optional[Dict[str, Any]] = Field(None, description="触发配置")

    # 时间配置
    advance_minutes: Optional[int] = Field(
        None, ge=0, le=1440, description="提前采集分钟数"
    )
    delay_minutes: Optional[int] = Field(
        None, ge=0, le=1440, description="延后采集分钟数"
    )
    repeat_interval_minutes: Optional[int] = Field(
        None, ge=1, le=1440, description="重复采集间隔(分钟)"
    )
    max_repeat_count: Optional[int] = Field(
        None, ge=1, le=100, description="最大重复次数"
    )

    # 采集配置
    custom_task_config: Optional[Dict[str, Any]] = Field(
        None, description="自定义任务配置"
    )
    priority_boost: Optional[int] = Field(None, ge=0, le=5, description="优先级提升")

    is_active: Optional[bool] = Field(None, description="是否启用")


class EventDrivenCrawlRuleResponse(BaseModel):
    """事件驱动采集规则响应"""

    id: int
    source_id: int
    rule_name: str

    # 触发条件
    trigger_type: str
    trigger_config: Dict[str, Any]

    # 时间配置
    advance_minutes: int
    delay_minutes: int
    repeat_interval_minutes: Optional[int]
    max_repeat_count: int

    # 采集配置
    custom_task_config: Dict[str, Any]
    priority_boost: int

    # 状态管理
    is_active: bool
    last_triggered_at: Optional[datetime]
    trigger_count: int
    success_count: int

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === 采集任务相关 Schemas ===

class CrawlTaskCreate(BaseModel):
    """采集任务创建请求"""

    source_id: int = Field(..., description="关联的数据源ID")
    task_type: str = Field(..., min_length=1, max_length=50, description="任务类型")

    # 任务来源
    trigger_type: TriggerType = Field(
        default=TriggerType.INTERVAL, description="触发类型"
    )
    related_event_id: Optional[int] = Field(None, description="关联的财经事件ID")
    trigger_rule_id: Optional[int] = Field(None, description="触发规则ID")

    target_url: Optional[str] = Field(None, max_length=1000, description="目标URL")
    task_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="任务配置"
    )

    scheduled_time: Optional[datetime] = Field(None, description="计划开始时间")
    max_retry_count: int = Field(default=3, ge=0, le=10, description="最大重试次数")


class CrawlTaskUpdate(BaseModel):
    """采集任务更新请求"""

    task_type: Optional[str] = Field(
        None, min_length=1, max_length=50, description="任务类型"
    )
    target_url: Optional[str] = Field(None, max_length=1000, description="目标URL")
    task_config: Optional[Dict[str, Any]] = Field(None, description="任务配置")

    scheduled_time: Optional[datetime] = Field(None, description="计划开始时间")
    started_time: Optional[datetime] = Field(None, description="实际开始时间")
    completed_time: Optional[datetime] = Field(None, description="完成时间")

    status: Optional[TaskStatus] = Field(None, description="任务状态")
    progress: Optional[int] = Field(None, ge=0, le=100, description="执行进度")

    worker_id: Optional[str] = Field(None, max_length=100, description="工作线程ID")
    execution_node: Optional[str] = Field(None, max_length=100, description="执行节点")

    items_found: Optional[int] = Field(None, ge=0, description="发现项目数")
    items_processed: Optional[int] = Field(None, ge=0, description="已处理项目数")
    items_success: Optional[int] = Field(None, ge=0, description="成功项目数")
    items_failed: Optional[int] = Field(None, ge=0, description="失败项目数")

    error_message: Optional[str] = Field(None, description="错误信息")
    retry_count: Optional[int] = Field(None, ge=0, description="重试次数")
    max_retry_count: Optional[int] = Field(
        None, ge=0, le=10, description="最大重试次数"
    )
    next_retry_time: Optional[datetime] = Field(None, description="下次重试时间")

    duration_seconds: Optional[int] = Field(None, ge=0, description="执行耗时(秒)")
    memory_usage_mb: Optional[int] = Field(None, ge=0, description="内存使用量(MB)")
    network_requests: Optional[int] = Field(None, ge=0, description="网络请求数")


class CrawlTaskResponse(BaseModel):
    """采集任务响应"""

    id: int
    source_id: int
    task_type: str

    # 任务来源
    trigger_type: str = "interval"
    related_event_id: Optional[int] = None
    trigger_rule_id: Optional[int] = None

    target_url: Optional[str] = None
    task_config: Dict[str, Any] = {}

    scheduled_time: Optional[datetime] = None
    started_time: Optional[datetime] = None
    completed_time: Optional[datetime] = None

    status: str = "pending"
    progress: Optional[int] = 0

    worker_id: Optional[str] = None
    execution_node: Optional[str] = None

    items_found: Optional[int] = 0
    items_processed: Optional[int] = 0
    items_success: Optional[int] = 0
    items_failed: Optional[int] = 0

    error_message: Optional[str] = None
    retry_count: Optional[int] = 0
    max_retry_count: int = 3
    next_retry_time: Optional[datetime] = None

    duration_seconds: Optional[int] = None
    memory_usage_mb: Optional[int] = None
    network_requests: Optional[int] = 0

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# === 数据源凭证相关 Schemas ===

class DataSourceCredentialCreate(BaseModel):
    """数据源凭证创建请求"""

    source_id: int = Field(..., description="关联的数据源ID")
    credential_type: CredentialType = Field(..., description="凭证类型")

    # 凭证数据（创建时传入明文，服务器端加密）
    credential_data: Dict[str, Any] = Field(..., description="凭证数据")

    expires_at: Optional[datetime] = Field(None, description="凭证过期时间")


class DataSourceCredentialUpdate(BaseModel):
    """数据源凭证更新请求"""

    credential_data: Optional[Dict[str, Any]] = Field(None, description="凭证数据")
    is_active: Optional[bool] = Field(None, description="是否启用")
    expires_at: Optional[datetime] = Field(None, description="凭证过期时间")
    validation_status: Optional[ValidationStatus] = Field(None, description="验证状态")


class DataSourceCredentialResponse(BaseModel):
    """数据源凭证响应"""

    id: int
    source_id: int
    credential_type: str

    # 注意：不返回加密数据
    encryption_method: str

    is_active: bool
    expires_at: Optional[datetime]
    last_validated: Optional[datetime]
    validation_status: str

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === 原始数据记录相关 Schemas ===

class RawDataRecordCreate(BaseModel):
    """原始数据记录创建请求"""

    task_id: Optional[int] = Field(None, description="关联的采集任务ID")
    source_id: int = Field(..., description="关联的数据源ID")

    # URL信息
    source_url: str = Field(..., max_length=1000, description="数据来源URL")
    canonical_url: Optional[str] = Field(None, max_length=1000, description="规范化URL")
    url_hash: str = Field(..., max_length=64, description="URL哈希值")
    url_domain: Optional[str] = Field(None, max_length=200, description="URL域名")

    # 内容标识
    content_hash: Optional[str] = Field(None, max_length=64, description="内容哈希值")
    content_simhash: Optional[int] = Field(None, description="内容相似性哈希")
    content_length: Optional[int] = Field(None, ge=0, description="内容长度")
    content_encoding: str = Field(
        default="utf-8", max_length=20, description="内容编码格式"
    )

    # 基础元数据
    title: Optional[str] = Field(None, max_length=1000, description="内容标题")
    author: Optional[str] = Field(None, max_length=200, description="作者信息")
    publish_time: Optional[datetime] = Field(None, description="发布时间")
    crawl_time: Optional[datetime] = Field(None, description="采集时间")

    # MongoDB引用
    mongodb_id: Optional[str] = Field(None, max_length=24, description="MongoDB文档ID")
    mongodb_collection: str = Field(
        default="raw_content", max_length=50, description="MongoDB集合名"
    )
    content_type: Optional[str] = Field(None, max_length=100, description="内容类型")

    # 处理状态
    processing_status: str = Field(
        default="pending", max_length=20, description="处理状态"
    )
    processing_priority: int = Field(default=5, ge=1, le=10, description="处理优先级")
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="质量评分")

    # 数据生命周期管理
    retention_policy: str = Field(
        default="standard", max_length=20, description="保留策略"
    )
    archive_after_days: int = Field(default=365, ge=1, description="归档天数")
    delete_after_days: int = Field(default=1095, ge=1, description="删除天数")


class RawDataRecordUpdate(BaseModel):
    """原始数据记录更新请求"""

    # 处理状态
    processing_status: Optional[str] = Field(
        None, max_length=20, description="处理状态"
    )
    processing_priority: Optional[int] = Field(
        None, ge=1, le=10, description="处理优先级"
    )
    quality_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="质量评分")

    # 数据生命周期管理
    retention_policy: Optional[str] = Field(None, max_length=20, description="保留策略")
    archive_after_days: Optional[int] = Field(None, ge=1, description="归档天数")
    delete_after_days: Optional[int] = Field(None, ge=1, description="删除天数")
    is_archived: Optional[bool] = Field(None, description="是否已归档")
    archived_at: Optional[datetime] = Field(None, description="归档时间")


class RawDataRecordResponse(BaseModel):
    """原始数据记录响应"""

    id: int
    task_id: Optional[int]
    source_id: int

    # URL信息
    source_url: str
    canonical_url: Optional[str]
    url_hash: str
    url_domain: Optional[str]

    # 内容标识
    content_hash: Optional[str]
    content_simhash: Optional[int]
    content_length: Optional[int]
    content_encoding: str

    # 基础元数据
    title: Optional[str]
    author: Optional[str]
    publish_time: Optional[datetime]
    crawl_time: datetime

    # MongoDB引用
    mongodb_id: Optional[str]
    mongodb_collection: str
    content_type: Optional[str]

    # 处理状态
    processing_status: str
    processing_priority: int
    quality_score: Optional[float]

    # 数据生命周期管理
    retention_policy: str
    archive_after_days: int
    delete_after_days: int
    is_archived: bool
    archived_at: Optional[datetime]

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# === 分页和列表响应 ===

class PaginatedResponse(BaseModel):
    """分页响应基类"""

    total: int = Field(..., description="总记录数")
    skip: int = Field(..., description="跳过的记录数")
    limit: int = Field(..., description="每页大小")


class DataSourceListResponse(PaginatedResponse):
    """数据源列表响应"""

    items: List[DataSourceResponse]


class EventDrivenCrawlRuleListResponse(PaginatedResponse):
    """事件驱动采集规则列表响应"""

    items: List[EventDrivenCrawlRuleResponse]


class CrawlTaskListResponse(PaginatedResponse):
    """采集任务列表响应"""

    items: List[CrawlTaskResponse]


class DataSourceConfigListResponse(PaginatedResponse):
    """数据源配置列表响应"""

    items: List[DataSourceConfigResponse]


class DataSourceCredentialListResponse(PaginatedResponse):
    """数据源凭证列表响应"""

    items: List[DataSourceCredentialResponse]


class RawDataRecordListResponse(PaginatedResponse):
    """原始数据记录列表响应"""

    items: List[RawDataRecordResponse]


# === 统计和健康状态响应 ===

class DataSourceHealthResponse(BaseModel):
    """数据源健康状态响应"""

    id: int
    name: str
    status: str
    health_score: Optional[Decimal] = Field(default=Decimal("1.00"), description="健康评分")
    success_rate_percentage: float
    health_status: str
    freshness_status: str
    last_success_time: Optional[datetime]
    consecutive_error_count: int

    @field_validator("health_score", mode="before")
    @classmethod
    def validate_health_score(cls, v):
        """处理health_score为None的情况"""
        if v is None:
            return Decimal("1.00")
        return v


class DataSourceStatsResponse(BaseModel):
    """数据源统计响应"""

    total_count: int
    active_count: int
    healthy_count: int
    avg_health_score: float
    avg_success_rate: float


class TaskExecutionStatsResponse(BaseModel):
    """任务执行统计响应"""

    total_tasks: int
    pending_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    avg_duration_seconds: Optional[float]
    success_rate_percentage: float


# === 批量操作相关 ===

class BatchStatusUpdateRequest(BaseModel):
    """批量状态更新请求"""

    record_ids: List[int] = Field(..., description="记录ID列表", min_items=1)
    new_status: str = Field(..., max_length=20, description="新状态")


class BatchOperationResponse(BaseModel):
    """批量操作响应"""

    success: bool = Field(..., description="操作是否成功")
    affected_count: int = Field(..., description="影响的记录数量")
    message: str = Field(..., description="操作结果消息")


# === 重复记录检测相关 ===

class DuplicateRecordInfo(BaseModel):
    """重复记录信息"""

    id: int = Field(..., description="记录ID")
    title: Optional[str] = Field(None, description="标题")
    source_url: str = Field(..., description="来源URL")
    created_at: datetime = Field(..., description="创建时间")


class DuplicateGroup(BaseModel):
    """重复记录组"""

    content_hash: str = Field(..., description="内容哈希值")
    count: int = Field(..., description="重复记录数量")
    records: List[DuplicateRecordInfo] = Field(..., description="重复记录列表")


class DuplicateRecordsResponse(BaseModel):
    """重复记录响应"""

    duplicates: List[DuplicateGroup] = Field(..., description="重复记录组列表")
    total_groups: int = Field(..., description="总组数")


class CrawlResult(BaseModel):
    """爬取结果"""

    success: bool = Field(..., description="爬取是否成功")
    message: str = Field(..., description="结果消息")
    data: Optional[Dict[str, Any]] = Field(None, description="爬取数据")



