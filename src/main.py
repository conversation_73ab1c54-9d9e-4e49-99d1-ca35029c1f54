"""
FinSight Backend 主应用入口
提供用户管理、金融信息处理等服务
"""

from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.core.config import settings
from src.core.json_response import UTF8JSONResponse
from src.core.database import create_tables
from src.services.data_collection_service.engines.crawler_engine import (
    start_crawler_engine,
    stop_crawler_engine,
)
from src.services.data_collection_service.engines.task_scheduler import (
    start_task_scheduler,
    stop_task_scheduler,
)
from src.services.data_processing_service.data_processing_manager import (
    start_data_processing_service,
    stop_data_processing_service,
)

from src.services.data_collection_service.admin_router import router as data_collection_admin_router
from src.services.financial_calendar_service.router import (
    router as financial_calendar_router,
)
from src.services.tag_classification_service.router import (
    router as tag_classification_router,
)
from src.services.tag_classification_service.admin_router import (
    router as tag_classification_admin_router,
)
from src.services.user_service.router import router as user_router
from src.services.user_service.admin_router import router as user_admin_router
from src.services.permission_service.router import router as permission_router
from src.services.permission_service.admin_router import (
    router as permission_admin_router,
)
from src.services.ai_service.admin_router import router as ai_admin_router
from src.services.data_processing_service.admin_router import router as data_processing_admin_router
from src.middleware.query_params_middleware import add_query_params_middleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    启动时根据配置创建数据库表，关闭时清理资源
    """
    # 启动时
    print("🚀 Starting FinSight Backend...")
    print(f"🔧 Environment: {settings.ENVIRONMENT}")
    print(f"🗄️ Database: {settings.DATABASE_URL}")
    print(f"🔒 Preserve data on restart: {settings.PRESERVE_DATA_ON_RESTART}")

    if settings.AUTO_CREATE_TABLES:
        create_tables()
    else:
        print("⏭️ Auto table creation disabled")

    # 启动数据采集相关服务
    print("🔄 Starting data collection services...")
    try:
        await start_crawler_engine()
        print("✅ Crawler engine started")

        await start_task_scheduler()
        print("✅ Task scheduler started")

    except Exception as e:
        print(f"❌ Failed to start data collection services: {e}")

    # 启动数据处理服务
    print("🔄 Starting data processing service...")
    try:
        await start_data_processing_service()
        print("✅ Data processing service started")

    except Exception as e:
        print(f"❌ Failed to start data processing service: {e}")

    yield

    # 关闭时
    print("🛑 Shutting down FinSight Backend...")
    try:
        await stop_data_processing_service()
        print("✅ Data processing service stopped")

        await stop_task_scheduler()
        print("✅ Task scheduler stopped")

        await stop_crawler_engine()
        print("✅ Crawler engine stopped")

    except Exception as e:
        print(f"❌ Error stopping services: {e}")


# 创建C端应用实例
app_c = FastAPI(
    title=f"{settings.APP_NAME} - C端API",
    version=settings.APP_VERSION,
    description="金融信息智能推送系统C端用户API",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url=None,
    openapi_url="/openapi.json" if settings.ENVIRONMENT == "development" else None,
    default_response_class=UTF8JSONResponse,
)

# 创建B端应用实例
app_b = FastAPI(
    title=f"{settings.APP_NAME} - B端管理API",
    version=settings.APP_VERSION,
    description="金融信息智能推送系统B端管理API",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url=None,
    openapi_url="/openapi.json" if settings.ENVIRONMENT == "development" else None,
    default_response_class=UTF8JSONResponse,
)

# 创建主应用实例
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="金融信息智能推送系统后端API",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url=None,
    openapi_url="/openapi.json" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan,
    default_response_class=UTF8JSONResponse,
)

# 配置CORS中间件
for application in [app, app_c, app_b]:
    application.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 添加查询参数清理中间件
for application in [app, app_c, app_b]:
    add_query_params_middleware(application)


# 全局异常处理器
async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器

    Args:
        request: HTTP请求对象
        exc: 异常对象

    Returns:
        JSON错误响应
    """
    if settings.DEBUG:
        # 开发环境返回详细错误信息
        return UTF8JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "detail": str(exc),
                "type": type(exc).__name__,
            },
        )
    else:
        # 生产环境返回通用错误信息
        return UTF8JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "detail": "An unexpected error occurred",
            },
        )


# 为所有应用添加异常处理器
for application in [app, app_c, app_b]:
    application.add_exception_handler(Exception, global_exception_handler)


# 健康检查接口
@app.get("/health")
async def health_check():
    """
    主应用健康检查接口

    Returns:
        应用状态信息
    """
    return {
        "status": "ok",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
    }


@app_c.get("/health")
async def health_check_c():
    """
    C端健康检查接口

    Returns:
        C端应用状态信息
    """
    return {
        "status": "ok",
        "app_name": f"{settings.APP_NAME} - C端",
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "api_type": "c_end",
    }


@app_b.get("/health")
async def health_check_b():
    """
    B端健康检查接口

    Returns:
        B端应用状态信息
    """
    return {
        "status": "ok",
        "app_name": f"{settings.APP_NAME} - B端",
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "api_type": "b_end",
    }


# C端根路径
@app_c.get("/")
async def root_c():
    """
    C端根路径接口

    Returns:
        C端欢迎信息
    """
    return {
        "message": f"Welcome to {settings.APP_NAME} - C端用户API",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "health": "/health",
    }


# B端根路径
@app_b.get("/")
async def root_b():
    """
    B端根路径接口

    Returns:
        B端欢迎信息
    """
    return {
        "message": f"Welcome to {settings.APP_NAME} - B端管理API",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "health": "/health",
    }


# 主应用根路径
@app.get("/")
async def root():
    """
    主应用根路径接口

    Returns:
        欢迎信息和API导航
    """
    return {
        "message": f"Welcome to {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "apis": {"c_end": "/c/docs", "b_end": "/b/docs", "main": "/docs"},
        "health": "/health",
    }


# 注册C端路由
app_c.include_router(user_router, prefix="/api/v1/users", tags=["用户管理-C端"])
app_c.include_router(
    permission_router, prefix="/api/v1/permissions", tags=["权限管理-C端"]
)
app_c.include_router(
    tag_classification_router, prefix="/api/v1/tags", tags=["标签分类-C端"]
)
app_c.include_router(
    financial_calendar_router, prefix="/api/v1/calendar", tags=["财经日历-C端"]
)
# 数据采集C端路由已移除，只保留B端管理接口

# 注册B端路由
app_b.include_router(
    user_admin_router, prefix="/api/v1/admin/users", tags=["用户管理-B端"]
)
app_b.include_router(
    permission_admin_router, prefix="/api/v1/admin/permissions", tags=["权限管理-B端"]
)
app_b.include_router(
    tag_classification_admin_router, prefix="/api/v1/admin", tags=["标签分类-B端"]
)
app_b.include_router(
    data_collection_admin_router, tags=["数据采集-B端"]
)
app_b.include_router(
    ai_admin_router, prefix="/api/v1/admin/ai", tags=["AI模型管理-B端"]
)

app_b.include_router(
    data_processing_admin_router, prefix="/api/v1/admin/data-processing", tags=["数据处理-B端"]
)

# 主应用挂载子应用
app.mount("/c", app_c)
app.mount("/b", app_b)

# 为主应用注册基本路由（保持向后兼容）
app.include_router(user_router, tags=["用户管理"])
app.include_router(permission_router, tags=["权限管理"])
app.include_router(tag_classification_router, tags=["标签分类"])
app.include_router(financial_calendar_router, tags=["财经日历"])
# 数据采集主应用路由已移除，只保留B端管理接口


# 其他服务路由可以在这里添加
# app.include_router(push_service_router, tags=["推送服务"])


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "src.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
